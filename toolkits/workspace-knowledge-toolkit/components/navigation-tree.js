/**
 * Navigation Tree Component for Workspace Knowledge Toolkit
 * Provides hierarchical navigation with collapsible nodes
 */

class NavigationTree {
    constructor(knowledgeIndex, crossReferences) {
        this.knowledgeIndex = knowledgeIndex;
        this.crossReferences = crossReferences;
        this.container = document.getElementById('navTreeContent');
        this.expandedNodes = new Set();
        this.selectedNode = null;
        
        this.initializeTree();
    }

    initializeTree() {
        if (!this.container) return;
        
        this.renderTree();
        this.initializeEventListeners();
    }

    renderTree() {
        const treeHtml = this.buildTreeStructure();
        this.container.innerHTML = treeHtml;
    }

    buildTreeStructure() {
        const categories = this.knowledgeIndex.resources;
        let html = '<div class="nav-tree-root">';

        // Build category nodes
        for (const [categoryKey, categoryData] of Object.entries(categories)) {
            html += this.createCategoryNode(categoryKey, categoryData);
        }

        html += '</div>';
        return html;
    }

    createCategoryNode(categoryKey, categoryData) {
        const isExpanded = this.expandedNodes.has(categoryKey);
        const hasItems = categoryData.items && categoryData.items.length > 0;
        const nodeId = `nav-${categoryKey}`;
        
        let html = `
            <div class="nav-tree-node nav-category-node" data-category="${categoryKey}">
                <div class="nav-node-header" data-node-id="${nodeId}">
                    ${hasItems ? `
                        <button class="nav-toggle ${isExpanded ? 'expanded' : ''}" 
                                data-target="${nodeId}-content">
                            ${isExpanded ? '▼' : '▶'}
                        </button>
                    ` : '<span class="nav-spacer"></span>'}
                    <span class="nav-icon">${categoryData.icon || '📁'}</span>
                    <span class="nav-label">${categoryData.category}</span>
                    <span class="nav-count">${categoryData.items?.length || 0}</span>
                </div>
        `;

        if (hasItems) {
            html += `
                <div class="nav-node-content ${isExpanded ? 'expanded' : 'collapsed'}" 
                     id="${nodeId}-content">
                    ${categoryData.items.map(item => this.createItemNode(item, categoryKey)).join('')}
                </div>
            `;
        }

        html += '</div>';
        return html;
    }

    createItemNode(item, categoryKey) {
        const nodeId = `nav-${categoryKey}-${item.id}`;
        const isSelected = this.selectedNode === item.id;
        
        return `
            <div class="nav-tree-node nav-item-node ${isSelected ? 'selected' : ''}" 
                 data-item-id="${item.id}" 
                 data-category="${categoryKey}">
                <div class="nav-node-header" data-node-id="${nodeId}">
                    <span class="nav-spacer"></span>
                    <span class="nav-icon">${this.getItemIcon(item)}</span>
                    <span class="nav-label" title="${item.description || item.title}">
                        ${item.title}
                    </span>
                    ${item.priority === 1 ? '<span class="nav-priority">⭐</span>' : ''}
                </div>
                ${this.createItemDetails(item)}
            </div>
        `;
    }

    createItemDetails(item) {
        return `
            <div class="nav-item-details">
                ${item.size ? `<span class="nav-detail-size">${item.size}</span>` : ''}
                ${item.tags ? `
                    <div class="nav-item-tags">
                        ${item.tags.slice(0, 2).map(tag => 
                            `<span class="nav-tag">${tag}</span>`
                        ).join('')}
                    </div>
                ` : ''}
            </div>
        `;
    }

    getItemIcon(item) {
        switch (item.type) {
            case 'markdown': return '📝';
            case 'html': return '🌐';
            case 'csv': return '📊';
            case 'json': return '🔧';
            default: return '📄';
        }
    }

    initializeEventListeners() {
        // Toggle expansion
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('nav-toggle')) {
                e.preventDefault();
                e.stopPropagation();
                this.handleToggleClick(e.target);
            }
        });

        // Item selection
        this.container.addEventListener('click', (e) => {
            const itemNode = e.target.closest('.nav-item-node');
            if (itemNode) {
                e.preventDefault();
                this.handleItemClick(itemNode);
            }
        });

        // Category header clicks
        this.container.addEventListener('click', (e) => {
            const categoryHeader = e.target.closest('.nav-category-node .nav-node-header');
            if (categoryHeader && !e.target.classList.contains('nav-toggle')) {
                this.handleCategoryClick(categoryHeader);
            }
        });
    }

    handleToggleClick(toggleButton) {
        const targetId = toggleButton.dataset.target;
        const targetContent = document.getElementById(targetId);
        const categoryKey = toggleButton.closest('.nav-category-node').dataset.category;
        
        if (targetContent) {
            const isExpanded = targetContent.classList.contains('expanded');
            
            if (isExpanded) {
                targetContent.classList.remove('expanded');
                targetContent.classList.add('collapsed');
                toggleButton.textContent = '▶';
                toggleButton.classList.remove('expanded');
                this.expandedNodes.delete(categoryKey);
            } else {
                targetContent.classList.remove('collapsed');
                targetContent.classList.add('expanded');
                toggleButton.textContent = '▼';
                toggleButton.classList.add('expanded');
                this.expandedNodes.add(categoryKey);
            }

            // Emit expansion event
            this.emitEvent('nodeToggled', {
                category: categoryKey,
                expanded: !isExpanded
            });
        }
    }

    handleItemClick(itemNode) {
        const itemId = itemNode.dataset.itemId;
        const category = itemNode.dataset.category;
        
        // Update selection
        this.setSelectedItem(itemId);
        
        // Emit selection event
        this.emitEvent('itemSelected', {
            id: itemId,
            category: category,
            item: this.findItemById(itemId, category)
        });
    }

    handleCategoryClick(categoryHeader) {
        const categoryNode = categoryHeader.closest('.nav-category-node');
        const categoryKey = categoryNode.dataset.category;
        
        // Emit category selection event
        this.emitEvent('categorySelected', {
            category: categoryKey,
            data: this.knowledgeIndex.resources[categoryKey]
        });
    }

    setSelectedItem(itemId) {
        // Remove previous selection
        const previousSelected = this.container.querySelector('.nav-item-node.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // Add new selection
        const newSelected = this.container.querySelector(`[data-item-id="${itemId}"]`);
        if (newSelected) {
            newSelected.classList.add('selected');
            this.selectedNode = itemId;
            
            // Scroll into view if needed
            newSelected.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'nearest' 
            });
        }
    }

    findItemById(itemId, category) {
        const categoryData = this.knowledgeIndex.resources[category];
        return categoryData?.items?.find(item => item.id === itemId);
    }

    expandCategory(categoryKey) {
        const categoryNode = this.container.querySelector(`[data-category="${categoryKey}"]`);
        if (categoryNode) {
            const toggleButton = categoryNode.querySelector('.nav-toggle');
            if (toggleButton && !toggleButton.classList.contains('expanded')) {
                this.handleToggleClick(toggleButton);
            }
        }
    }

    collapseCategory(categoryKey) {
        const categoryNode = this.container.querySelector(`[data-category="${categoryKey}"]`);
        if (categoryNode) {
            const toggleButton = categoryNode.querySelector('.nav-toggle');
            if (toggleButton && toggleButton.classList.contains('expanded')) {
                this.handleToggleClick(toggleButton);
            }
        }
    }

    filterTree(query) {
        if (!query) {
            this.showAllNodes();
            return;
        }

        const queryLower = query.toLowerCase();
        const matchingItems = [];

        // Find matching items
        for (const [categoryKey, categoryData] of Object.entries(this.knowledgeIndex.resources)) {
            if (categoryData.items) {
                categoryData.items.forEach(item => {
                    if (this.itemMatchesQuery(item, queryLower)) {
                        matchingItems.push({ item, category: categoryKey });
                    }
                });
            }
        }

        this.highlightMatchingItems(matchingItems, queryLower);
    }

    itemMatchesQuery(item, query) {
        return item.title.toLowerCase().includes(query) ||
               item.description?.toLowerCase().includes(query) ||
               item.tags?.some(tag => tag.toLowerCase().includes(query));
    }

    highlightMatchingItems(matchingItems, query) {
        // Hide all items first
        this.container.querySelectorAll('.nav-item-node').forEach(node => {
            node.style.display = 'none';
        });

        // Show and highlight matching items
        matchingItems.forEach(({ item, category }) => {
            const itemNode = this.container.querySelector(`[data-item-id="${item.id}"]`);
            if (itemNode) {
                itemNode.style.display = 'block';
                this.expandCategory(category);
                
                // Highlight matching text
                const label = itemNode.querySelector('.nav-label');
                if (label) {
                    const originalText = label.textContent;
                    const highlightedText = originalText.replace(
                        new RegExp(`(${query})`, 'gi'),
                        '<mark>$1</mark>'
                    );
                    label.innerHTML = highlightedText;
                }
            }
        });
    }

    showAllNodes() {
        this.container.querySelectorAll('.nav-item-node').forEach(node => {
            node.style.display = 'block';
            
            // Remove highlights
            const label = node.querySelector('.nav-label');
            if (label && label.innerHTML.includes('<mark>')) {
                label.textContent = label.textContent; // This removes HTML tags
            }
        });
    }

    emitEvent(eventName, detail) {
        const event = new CustomEvent(`navTree${eventName}`, {
            detail: detail,
            bubbles: true
        });
        this.container.dispatchEvent(event);
    }

    // Public API methods
    getSelectedItem() {
        return this.selectedNode;
    }

    selectItem(itemId) {
        this.setSelectedItem(itemId);
    }

    refresh() {
        this.renderTree();
    }
}

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationTree;
} else {
    window.NavigationTree = NavigationTree;
}
