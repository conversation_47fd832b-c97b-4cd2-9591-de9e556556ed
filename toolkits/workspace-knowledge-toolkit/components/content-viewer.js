/**
 * Content Viewer Component for Workspace Knowledge Toolkit
 * Handles display of different content types with enhanced features
 */

class ContentViewer {
    constructor(parsers, crossReferences) {
        this.markdownParser = parsers.markdown;
        this.csvParser = parsers.csv;
        this.jsonParser = parsers.json;
        this.crossReferences = crossReferences;
        
        this.contentDisplay = document.getElementById('contentDisplay');
        this.contentHeader = document.getElementById('contentHeader');
        this.contentBody = document.getElementById('contentBody');
        this.contentFooter = document.getElementById('contentFooter');
        this.welcomeScreen = document.getElementById('welcomeScreen');
        
        this.currentContent = null;
        this.loadingState = false;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Listen for content requests
        document.addEventListener('navTreeitemSelected', (e) => {
            this.loadContent(e.detail.item, e.detail.category);
        });

        document.addEventListener('searchResultSelected', (e) => {
            this.loadContentById(e.detail.id, e.detail.category);
        });

        // Content interaction events
        this.contentBody.addEventListener('click', (e) => {
            this.handleContentClick(e);
        });

        // Scroll events for reading progress
        this.contentBody.addEventListener('scroll', (e) => {
            this.updateReadingProgress(e);
        });
    }

    async loadContent(item, category) {
        if (!item || !item.path) {
            this.showError('Invalid content item');
            return;
        }

        this.showLoadingState();
        
        try {
            const content = await this.fetchContent(item.path);
            await this.displayContent(item, category, content);
            this.updateBreadcrumb(item, category);
            this.showRelatedContent(item);
        } catch (error) {
            console.error('Error loading content:', error);
            this.showError(`Failed to load content: ${error.message}`);
        }
    }

    async loadContentById(itemId, category) {
        // Find the item in the knowledge index
        const categoryData = window.knowledgeIndex?.resources[category];
        const item = categoryData?.items?.find(i => i.id === itemId);
        
        if (item) {
            await this.loadContent(item, category);
        } else {
            this.showError('Content not found');
        }
    }

    async fetchContent(path) {
        const response = await fetch(path);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.text();
    }

    async displayContent(item, category, content) {
        this.currentContent = { item, category, content };
        
        // Hide welcome screen and show content display
        this.welcomeScreen.style.display = 'none';
        this.contentDisplay.style.display = 'block';
        
        // Render header
        this.renderContentHeader(item, category);
        
        // Render content based on type
        await this.renderContentBody(item, content);
        
        // Render footer
        this.renderContentFooter(item);
        
        // Scroll to top
        this.contentBody.scrollTop = 0;
        
        // Emit content loaded event
        this.emitEvent('contentLoaded', { item, category });
    }

    renderContentHeader(item, category) {
        const categoryData = window.knowledgeIndex?.resources[category];
        
        this.contentHeader.innerHTML = `
            <div class="content-title-section">
                <div class="content-category">
                    <span class="category-icon">${categoryData?.icon || '📄'}</span>
                    <span class="category-name">${categoryData?.category || category}</span>
                </div>
                <h1 class="content-title">${item.title}</h1>
                ${item.description ? `<p class="content-description">${item.description}</p>` : ''}
            </div>
            
            <div class="content-meta">
                <div class="content-meta-items">
                    ${item.size ? `<span class="meta-item"><span class="meta-icon">📏</span>${item.size}</span>` : ''}
                    ${item.type ? `<span class="meta-item"><span class="meta-icon">📋</span>${item.type.toUpperCase()}</span>` : ''}
                    ${item.priority ? `<span class="meta-item priority-${item.priority}"><span class="meta-icon">⭐</span>Priority ${item.priority}</span>` : ''}
                </div>
                
                <div class="content-actions">
                    <button class="action-btn" id="bookmarkBtn" title="Bookmark this content">
                        <span class="action-icon">⭐</span>
                    </button>
                    <button class="action-btn" id="shareBtn" title="Share this content">
                        <span class="action-icon">🔗</span>
                    </button>
                    <button class="action-btn" id="printBtn" title="Print this content">
                        <span class="action-icon">🖨️</span>
                    </button>
                    <button class="action-btn" id="fullscreenBtn" title="Fullscreen view">
                        <span class="action-icon">⛶</span>
                    </button>
                </div>
            </div>
            
            ${item.tags ? `
                <div class="content-tags">
                    ${item.tags.map(tag => `<span class="content-tag">${tag}</span>`).join('')}
                </div>
            ` : ''}
        `;

        // Add action event listeners
        this.addHeaderActionListeners(item);
    }

    async renderContentBody(item, content) {
        let renderedContent = '';
        
        switch (item.type) {
            case 'markdown':
                renderedContent = this.markdownParser.parse(content, item.id);
                break;
            case 'csv':
                renderedContent = this.csvParser.parse(content, item.id);
                break;
            case 'json':
                renderedContent = this.jsonParser.parse(content, item.id);
                break;
            case 'html':
                // For HTML content, create an iframe
                renderedContent = this.createIframeContent(item.path);
                break;
            default:
                renderedContent = `<pre class="raw-content">${this.escapeHtml(content)}</pre>`;
        }
        
        this.contentBody.innerHTML = `
            <div class="content-wrapper">
                ${renderedContent}
            </div>
        `;

        // Initialize content-specific features
        await this.initializeContentFeatures(item.type);
    }

    createIframeContent(path) {
        return `
            <div class="iframe-container">
                <iframe src="${path}" 
                        class="content-iframe" 
                        frameborder="0" 
                        sandbox="allow-scripts allow-same-origin allow-forms">
                </iframe>
            </div>
        `;
    }

    async initializeContentFeatures(contentType) {
        switch (contentType) {
            case 'markdown':
                this.initializeMarkdownFeatures();
                break;
            case 'csv':
                this.initializeCsvFeatures();
                break;
            case 'json':
                this.initializeJsonFeatures();
                break;
        }
    }

    initializeMarkdownFeatures() {
        // Add copy code functionality
        this.contentBody.querySelectorAll('.copy-code').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const codeBlock = e.target.closest('.code-block').querySelector('code');
                this.copyToClipboard(codeBlock.textContent);
                this.showToast('Code copied to clipboard!');
            });
        });

        // Add heading anchor functionality
        this.contentBody.querySelectorAll('.heading-anchor').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const headingId = anchor.getAttribute('href').substring(1);
                this.scrollToHeading(headingId);
                this.updateUrl(headingId);
            });
        });

        // Generate table of contents
        this.generateTableOfContents();
    }

    initializeCsvFeatures() {
        // CSV table functionality is handled by the CSV parser
        // Add any additional features here
        if (typeof initializeCsvTable === 'function') {
            const tables = this.contentBody.querySelectorAll('.csv-table');
            tables.forEach(table => {
                initializeCsvTable(table.id);
            });
        }
    }

    initializeJsonFeatures() {
        // JSON viewer functionality is handled by the JSON parser
        // Add any additional features here
        if (typeof initializeJsonViewer === 'function') {
            const viewers = this.contentBody.querySelectorAll('.json-container');
            viewers.forEach(viewer => {
                initializeJsonViewer(viewer.id);
            });
        }
    }

    renderContentFooter(item) {
        const relatedItems = this.getRelatedContent(item);
        
        this.contentFooter.innerHTML = `
            <div class="content-footer-section">
                <div class="reading-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="readingProgress"></div>
                    </div>
                    <span class="progress-text">Reading Progress</span>
                </div>
                
                ${relatedItems.length > 0 ? `
                    <div class="related-content">
                        <h3>Related Content</h3>
                        <div class="related-items">
                            ${relatedItems.map(related => `
                                <div class="related-item" data-id="${related.id}" data-category="${related.category}">
                                    <span class="related-icon">${this.getItemIcon(related)}</span>
                                    <span class="related-title">${related.title}</span>
                                    <span class="related-type">${related.type}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;

        // Add related content click handlers
        this.contentFooter.querySelectorAll('.related-item').forEach(item => {
            item.addEventListener('click', () => {
                const id = item.dataset.id;
                const category = item.dataset.category;
                this.loadContentById(id, category);
            });
        });
    }

    getRelatedContent(item) {
        const crossRefs = this.crossReferences.crossReferences[item.id];
        if (!crossRefs) return [];

        return crossRefs.references.map(ref => {
            // Find the referenced item
            for (const [categoryKey, categoryData] of Object.entries(window.knowledgeIndex.resources)) {
                const foundItem = categoryData.items?.find(i => i.id === ref.target);
                if (foundItem) {
                    return { ...foundItem, category: categoryKey };
                }
            }
            return null;
        }).filter(Boolean);
    }

    addHeaderActionListeners(item) {
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        const shareBtn = document.getElementById('shareBtn');
        const printBtn = document.getElementById('printBtn');
        const fullscreenBtn = document.getElementById('fullscreenBtn');

        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', () => this.toggleBookmark(item));
        }

        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.shareContent(item));
        }

        if (printBtn) {
            printBtn.addEventListener('click', () => this.printContent());
        }

        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        }
    }

    showLoadingState() {
        this.loadingState = true;
        this.welcomeScreen.style.display = 'none';
        this.contentDisplay.style.display = 'block';
        
        this.contentHeader.innerHTML = '<div class="loading-header">Loading...</div>';
        this.contentBody.innerHTML = `
            <div class="content-loading">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <div class="loading-text">Loading content...</div>
            </div>
        `;
        this.contentFooter.innerHTML = '';
    }

    showError(message) {
        this.loadingState = false;
        this.welcomeScreen.style.display = 'none';
        this.contentDisplay.style.display = 'block';
        
        this.contentHeader.innerHTML = '<div class="error-header">Error</div>';
        this.contentBody.innerHTML = `
            <div class="content-error">
                <div class="error-icon">⚠️</div>
                <div class="error-title">Content Load Error</div>
                <div class="error-message">${message}</div>
                <button class="error-retry" onclick="location.reload()">Retry</button>
            </div>
        `;
        this.contentFooter.innerHTML = '';
    }

    showWelcomeScreen() {
        this.contentDisplay.style.display = 'none';
        this.welcomeScreen.style.display = 'block';
        this.currentContent = null;
    }

    updateBreadcrumb(item, category) {
        const breadcrumb = document.getElementById('breadcrumb');
        const categoryData = window.knowledgeIndex?.resources[category];
        
        breadcrumb.innerHTML = `
            <span class="breadcrumb-item" onclick="contentViewer.showWelcomeScreen()">
                <span class="breadcrumb-icon">🏠</span>
                Home
            </span>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item">
                <span class="breadcrumb-icon">${categoryData?.icon || '📁'}</span>
                ${categoryData?.category || category}
            </span>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item active">
                <span class="breadcrumb-icon">${this.getItemIcon(item)}</span>
                ${item.title}
            </span>
        `;
    }

    getItemIcon(item) {
        switch (item.type) {
            case 'markdown': return '📝';
            case 'html': return '🌐';
            case 'csv': return '📊';
            case 'json': return '🔧';
            default: return '📄';
        }
    }

    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).catch(err => {
            console.error('Failed to copy text: ', err);
        });
    }

    showToast(message) {
        // Implementation would show a toast notification
        console.log('Toast:', message);
    }

    emitEvent(eventName, detail) {
        const event = new CustomEvent(`contentViewer${eventName}`, {
            detail: detail,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Public API methods
    getCurrentContent() {
        return this.currentContent;
    }

    isLoading() {
        return this.loadingState;
    }
}

// Export for use in main application
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContentViewer;
} else {
    window.ContentViewer = ContentViewer;
}
