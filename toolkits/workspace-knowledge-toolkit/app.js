/**
 * Workspace Knowledge Toolkit - Main Application
 * Integrates all components and manages the overall application state
 */

class WorkspaceKnowledgeApp {
    constructor() {
        this.knowledgeIndex = null;
        this.searchIndex = null;
        this.crossReferences = null;
        
        this.searchComponent = null;
        this.navigationTree = null;
        this.contentViewer = null;
        
        this.currentTheme = 'light';
        this.isInitialized = false;
        
        this.initializeApp();
    }

    async initializeApp() {
        try {
            // Show loading overlay
            this.showLoadingOverlay('Initializing knowledge toolkit...');
            
            // Load data files
            await this.loadDataFiles();
            
            // Initialize parsers
            this.initializeParsers();
            
            // Initialize components
            this.initializeComponents();
            
            // Initialize theme
            this.initializeTheme();
            
            // Initialize event listeners
            this.initializeEventListeners();
            
            // Initialize welcome screen
            this.initializeWelcomeScreen();
            
            // Hide loading overlay
            this.hideLoadingOverlay();
            
            this.isInitialized = true;
            console.log('Workspace Knowledge Toolkit initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showError('Failed to initialize the knowledge toolkit. Please refresh the page.');
        }
    }

    async loadDataFiles() {
        try {
            const [knowledgeResponse, searchResponse, crossRefResponse] = await Promise.all([
                fetch('data/knowledge-index.json'),
                fetch('data/search-index.json'),
                fetch('data/cross-references.json')
            ]);

            if (!knowledgeResponse.ok || !searchResponse.ok || !crossRefResponse.ok) {
                throw new Error('Failed to load data files');
            }

            this.knowledgeIndex = await knowledgeResponse.json();
            this.searchIndex = await searchResponse.json();
            this.crossReferences = await crossRefResponse.json();
            
            // Make knowledge index globally available
            window.knowledgeIndex = this.knowledgeIndex;
            
        } catch (error) {
            throw new Error(`Data loading failed: ${error.message}`);
        }
    }

    initializeParsers() {
        this.parsers = {
            markdown: new MarkdownParser(this.crossReferences),
            csv: new CsvParser(),
            json: new JsonParser()
        };
    }

    initializeComponents() {
        // Initialize search component
        this.searchComponent = new SearchComponent(this.searchIndex, this.knowledgeIndex);
        
        // Initialize navigation tree
        this.navigationTree = new NavigationTree(this.knowledgeIndex, this.crossReferences);
        
        // Initialize content viewer
        this.contentViewer = new ContentViewer(this.parsers, this.crossReferences);
        
        // Make components globally available for debugging
        window.searchComponent = this.searchComponent;
        window.navigationTree = this.navigationTree;
        window.contentViewer = this.contentViewer;
    }

    initializeTheme() {
        // Load saved theme preference
        const savedTheme = localStorage.getItem('workspaceKnowledgeTheme') || 'light';
        this.setTheme(savedTheme);
        
        // Theme toggle button
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Listen for theme changes from parent (if in iframe)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                this.setTheme(event.data.theme);
            }
        });
    }

    initializeEventListeners() {
        // Sidebar controls
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarRefresh = document.getElementById('sidebarRefresh');
        
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
        
        if (sidebarRefresh) {
            sidebarRefresh.addEventListener('click', () => {
                this.refreshData();
            });
        }

        // Quick access buttons
        document.querySelectorAll('.quick-access-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.target.dataset.category;
                this.handleQuickAccess(category);
            });
        });

        // Category filters
        document.querySelectorAll('.filter-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateCategoryFilters();
            });
        });

        // Welcome screen actions
        document.querySelectorAll('.welcome-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                this.handleWelcomeAction(action);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
    }

    initializeWelcomeScreen() {
        this.updateWelcomeStats();
        this.loadFeaturedResources();
    }

    updateWelcomeStats() {
        const totalResources = document.getElementById('totalResources');
        const totalCategories = document.getElementById('totalCategories');
        const totalToolkits = document.getElementById('totalToolkits');

        if (totalResources) {
            totalResources.textContent = this.knowledgeIndex.metadata.totalResources;
        }
        
        if (totalCategories) {
            totalCategories.textContent = this.knowledgeIndex.metadata.categories.length;
        }
        
        if (totalToolkits) {
            // Count toolkit items
            const toolkitCount = this.knowledgeIndex.resources.toolkits?.items?.length || 0;
            totalToolkits.textContent = toolkitCount;
        }
    }

    loadFeaturedResources() {
        const featuredContainer = document.getElementById('featuredResources');
        if (!featuredContainer) return;

        const featuredItems = [];
        
        // Get priority 1 items from each category
        for (const [categoryKey, categoryData] of Object.entries(this.knowledgeIndex.resources)) {
            if (categoryData.items) {
                const priorityItems = categoryData.items
                    .filter(item => item.priority === 1)
                    .slice(0, 2); // Limit to 2 per category
                
                priorityItems.forEach(item => {
                    featuredItems.push({ ...item, category: categoryKey, categoryData });
                });
            }
        }

        featuredContainer.innerHTML = featuredItems.map(item => `
            <div class="featured-resource-card" data-id="${item.id}" data-category="${item.category}">
                <div class="featured-card-header">
                    <span class="featured-icon">${item.categoryData.icon}</span>
                    <span class="featured-category">${item.categoryData.category}</span>
                </div>
                <h4 class="featured-title">${item.title}</h4>
                <p class="featured-description">${item.description || 'No description available'}</p>
                <div class="featured-meta">
                    ${item.size ? `<span class="featured-size">${item.size}</span>` : ''}
                    <span class="featured-type">${item.type.toUpperCase()}</span>
                </div>
            </div>
        `).join('');

        // Add click handlers
        featuredContainer.querySelectorAll('.featured-resource-card').forEach(card => {
            card.addEventListener('click', () => {
                const id = card.dataset.id;
                const category = card.dataset.category;
                this.contentViewer.loadContentById(id, category);
            });
        });
    }

    // Theme management
    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
        
        // Save preference
        localStorage.setItem('workspaceKnowledgeTheme', theme);
        
        // Notify parent if in iframe
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({ 
                type: 'THEME_CHANGE', 
                theme: theme 
            }, '*');
        }
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    // Event handlers
    handleQuickAccess(category) {
        const quickAccessMap = this.searchIndex.quickAccess[category];
        if (quickAccessMap && quickAccessMap.length > 0) {
            // Load the first item in the quick access category
            const firstItemId = quickAccessMap[0];
            
            // Find the item and its category
            for (const [categoryKey, categoryData] of Object.entries(this.knowledgeIndex.resources)) {
                const item = categoryData.items?.find(i => i.id === firstItemId);
                if (item) {
                    this.contentViewer.loadContent(item, categoryKey);
                    this.navigationTree.selectItem(firstItemId);
                    return;
                }
            }
        }
    }

    handleWelcomeAction(action) {
        switch (action) {
            case 'explore':
                // Expand all categories in navigation
                this.navigationTree.expandAll();
                break;
            case 'search':
                // Focus search input
                this.searchComponent.focus();
                break;
            case 'learning':
                // Load learning paths
                this.handleQuickAccess('learning');
                break;
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.searchComponent.focus();
        }
        
        // Escape to clear search or go back
        if (e.key === 'Escape') {
            if (this.searchComponent.getQuery()) {
                this.searchComponent.clearSearch();
            } else if (this.contentViewer.getCurrentContent()) {
                this.contentViewer.showWelcomeScreen();
            }
        }
        
        // Ctrl/Cmd + B to toggle sidebar
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebar();
        }
    }

    updateCategoryFilters() {
        const checkedCategories = Array.from(document.querySelectorAll('.filter-checkbox:checked'))
            .map(cb => cb.dataset.category);
        
        // Update navigation tree visibility
        document.querySelectorAll('.nav-category-node').forEach(node => {
            const category = node.dataset.category;
            node.style.display = checkedCategories.includes(category) ? 'block' : 'none';
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
        }
    }

    async refreshData() {
        try {
            this.showLoadingOverlay('Refreshing data...');
            await this.loadDataFiles();
            
            // Refresh components
            this.searchComponent.setSearchIndex(this.searchIndex);
            this.searchComponent.setKnowledgeIndex(this.knowledgeIndex);
            this.navigationTree.refresh();
            
            this.hideLoadingOverlay();
            this.showToast('Data refreshed successfully');
            
        } catch (error) {
            this.hideLoadingOverlay();
            this.showToast('Failed to refresh data', 'error');
        }
    }

    handleWindowResize() {
        // Handle responsive layout changes
        const isMobile = window.innerWidth < 768;
        const sidebar = document.getElementById('sidebar');
        
        if (sidebar) {
            if (isMobile) {
                sidebar.classList.add('mobile');
            } else {
                sidebar.classList.remove('mobile');
            }
        }
    }

    // Utility methods
    showLoadingOverlay(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        const loadingText = overlay?.querySelector('.loading-text');
        
        if (overlay) {
            overlay.style.display = 'flex';
        }
        
        if (loadingText) {
            loadingText.textContent = message;
        }
    }

    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    showError(message) {
        console.error(message);
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${type === 'error' ? '⚠️' : 'ℹ️'}</span>
                <span class="toast-message">${message}</span>
                <button class="toast-close">✕</button>
            </div>
        `;

        toastContainer.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);

        // Manual close
        toast.querySelector('.toast-close').addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }

    // Public API
    getKnowledgeIndex() {
        return this.knowledgeIndex;
    }

    getSearchIndex() {
        return this.searchIndex;
    }

    getCrossReferences() {
        return this.crossReferences;
    }

    isReady() {
        return this.isInitialized;
    }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.workspaceKnowledgeApp = new WorkspaceKnowledgeApp();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WorkspaceKnowledgeApp;
}
