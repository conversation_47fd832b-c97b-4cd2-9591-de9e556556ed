{"crossReferences": {"ultimate-knowledge-base": {"references": [{"target": "main-readme", "type": "overview", "description": "Complete enterprise platform overview"}, {"target": "docs-readme", "type": "navigation", "description": "Documentation hub navigation"}, {"target": "learning-paths", "type": "learning", "description": "Career progression framework"}, {"target": "algorithm-readme", "type": "specialization", "description": "Algorithm mastery specialization"}, {"target": "patterns-readme", "type": "specialization", "description": "Design patterns specialization"}]}, "algorithm-readme": {"references": [{"target": "algorithm-index", "type": "navigation", "description": "Complete algorithm cross-reference"}, {"target": "algorithm-learning-path", "type": "progression", "description": "4-level mastery roadmap"}, {"target": "algorithm-getting-started", "type": "beginner", "description": "30-day beginner program"}, {"target": "patterns-readme", "type": "related", "description": "Complementary design patterns knowledge"}]}, "patterns-readme": {"references": [{"target": "patterns-index", "type": "navigation", "description": "Comprehensive pattern cross-reference"}, {"target": "patterns-learning-path", "type": "progression", "description": "Structured learning roadmap"}, {"target": "patterns-selection-guide", "type": "decision", "description": "Pattern selection framework"}, {"target": "algorithm-readme", "type": "related", "description": "Complementary algorithm knowledge"}]}, "golang-handbook": {"references": [{"target": "golang-data-types", "type": "reference", "description": "Data types reference table"}, {"target": "golang-best-practices", "type": "reference", "description": "Best practices reference table"}, {"target": "golang-toolkit", "type": "interactive", "description": "Interactive Golang toolkit"}, {"target": "main-readme", "type": "context", "description": "Enterprise platform context"}]}, "unified-toolkit": {"references": [{"target": "golang-toolkit", "type": "component", "description": "Reference implementation example"}, {"target": "architecture-explorer", "type": "component", "description": "Architecture knowledge toolkit"}, {"target": "thinking-os-toolkit", "type": "component", "description": "Cognitive framework toolkit"}]}}, "linkPatterns": {"documentation": {"internal": "../../docs/", "external": "https://", "relative": "../"}, "algorithms": {"internal": "../../algorithm-mastery-system/", "examples": "../../algorithm-mastery-system/", "implementations": "../../algorithm-mastery-system/"}, "patterns": {"internal": "../../design-patterns/", "examples": "../../design-patterns/examples/", "patterns": "../../design-patterns/patterns/"}, "toolkits": {"internal": "../", "unified": "../unified-toolkit/", "individual": "../"}}, "navigationHierarchy": {"root": {"ultimate-knowledge-base": {"level": 0, "children": ["main-readme", "docs-readme", "learning-paths"]}}, "documentation": {"docs-readme": {"level": 1, "children": ["learning-paths", "quick-reference", "deployment-guide"]}}, "algorithms": {"algorithm-readme": {"level": 1, "children": ["algorithm-index", "algorithm-learning-path", "algorithm-getting-started"]}}, "patterns": {"patterns-readme": {"level": 1, "children": ["patterns-index", "patterns-learning-path", "patterns-selection-guide"]}}, "toolkits": {"unified-toolkit": {"level": 1, "children": ["golang-toolkit", "architecture-explorer", "thinking-os-toolkit"]}}}, "semanticRelations": {"prerequisites": {"algorithm-learning-path": ["algorithm-getting-started"], "patterns-learning-path": ["patterns-getting-started"], "deployment-guide": ["main-readme", "docs-readme"]}, "complements": {"algorithm-readme": ["patterns-readme"], "patterns-readme": ["algorithm-readme"], "golang-handbook": ["golang-data-types", "golang-best-practices"]}, "implementations": {"unified-toolkit": ["golang-toolkit", "architecture-explorer"], "golang-toolkit": ["golang-handbook", "golang-data-types"]}}}