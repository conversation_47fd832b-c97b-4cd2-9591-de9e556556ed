<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Engineering Master Handbook</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/themes/prism-tomorrow.min.css" rel="stylesheet" data-theme="dark">
</head>
<body>
    <!-- Progress Bar -->
    <div class="progress-bar">
        <div class="progress-fill"></div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">Frontend Engineering Master Handbook</h1>
            <div class="header-controls">
                <button class="btn btn--secondary theme-toggle" id="themeToggle" aria-label="Chuyển đổi chế độ tối/sáng">
                    <span class="theme-icon">🌙</span>
                </button>
                <button class="btn btn--secondary sidebar-toggle" id="sidebarToggle" aria-label="Ẩn/hiện menu">
                    <span class="hamburger">☰</span>
                </button>
            </div>
        </div>
        
        <!-- Search Bar -->
        <div class="search-container">
            <input type="text" class="search-input" id="searchInput" placeholder="Tìm kiếm kiến thức...">
            <div class="search-results" id="searchResults"></div>
        </div>
    </header>

    <div class="main-wrapper">
        <!-- Sidebar Navigation -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <div class="nav-section">
                    <h3 class="nav-title">Mục lục</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <button class="nav-button" data-section="fundamentals">
                                <span class="nav-icon">🏗️</span>
                                <span class="nav-text">Web Fundamentals</span>
                                <span class="nav-arrow">▶</span>
                            </button>
                            <ul class="nav-submenu">
                                <li><a href="#html-structure" class="nav-link">HTML Structure & Semantics</a></li>
                                <li><a href="#css-fundamentals" class="nav-link">CSS Box Model & Cascade</a></li>
                                <li><a href="#javascript-core" class="nav-link">JavaScript Fundamentals</a></li>
                                <li><a href="#dom-manipulation" class="nav-link">DOM Manipulation</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="core-principles">
                                <span class="nav-icon">⚡</span>
                                <span class="nav-text">Core Principles</span>
                                <span class="nav-arrow">▶</span>
                            </button>
                            <ul class="nav-submenu">
                                <li><a href="#separation-concerns" class="nav-link">Separation of Concerns</a></li>
                                <li><a href="#progressive-enhancement" class="nav-link">Progressive Enhancement</a></li>
                                <li><a href="#accessibility" class="nav-link">Web Accessibility</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="modern-development">
                                <span class="nav-icon">🚀</span>
                                <span class="nav-text">Modern Development</span>
                                <span class="nav-arrow">▶</span>
                            </button>
                            <ul class="nav-submenu">
                                <li><a href="#react-fundamentals" class="nav-link">React Concepts & Hooks</a></li>
                                <li><a href="#nextjs-concepts" class="nav-link">Next.js Architecture</a></li>
                                <li><a href="#typescript-integration" class="nav-link">TypeScript Benefits</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="architecture-patterns">
                                <span class="nav-icon">🏛️</span>
                                <span class="nav-text">Architecture & Patterns</span>
                                <span class="nav-arrow">▶</span>
                            </button>
                            <ul class="nav-submenu">
                                <li><a href="#solid-principles" class="nav-link">SOLID Principles</a></li>
                                <li><a href="#component-patterns" class="nav-link">Component Patterns</a></li>
                                <li><a href="#state-management" class="nav-link">State Management</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="performance">
                                <span class="nav-icon">⚡</span>
                                <span class="nav-text">Performance & Optimization</span>
                                <span class="nav-arrow">▶</span>
                            </button>
                            <ul class="nav-submenu">
                                <li><a href="#rendering-optimization" class="nav-link">Rendering Optimization</a></li>
                                <li><a href="#javascript-optimization" class="nav-link">JavaScript Optimization</a></li>
                                <li><a href="#core-web-vitals" class="nav-link">Core Web Vitals</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="mainContent">
            <!-- Introduction Section -->
            <section class="content-section intro-section">
                <div class="intro-hero">
                    <h1>Frontend Engineering Master Handbook</h1>
                    <p class="intro-subtitle">Cẩm nang toàn diện từ JavaScript Developer tầm trung đến Principle Frontend Engineer</p>
                    <div class="intro-features">
                        <div class="feature-card">
                            <h3>🎯 Nguyên lý bất biến</h3>
                            <p>HTML, CSS, JavaScript và các khái niệm cốt lõi không thay đổi theo thời gian</p>
                        </div>
                        <div class="feature-card">
                            <h3>🚀 Công nghệ hiện đại</h3>
                            <p>React, Next.js, TypeScript và ecosystem tools cập nhật nhất</p>
                        </div>
                        <div class="feature-card">
                            <h3>🏗️ Kiến trúc hệ thống</h3>
                            <p>Design patterns, clean architecture và best practices</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Web Fundamentals Section -->
            <section class="content-section" id="fundamentals">
                <div class="section-header">
                    <h2 class="section-title">🏗️ Web Fundamentals</h2>
                    <p class="section-description">Nền tảng cứng bất biến - những nguyên lý thiết kế web cốt lõi</p>
                </div>

                <article class="subsection" id="html-structure">
                    <div class="subsection-header">
                        <h3>HTML Structure & Semantics</h3>
                        <button class="bookmark-btn" data-bookmark="html-structure">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>HTML tạo nên nền tảng của mọi trang web. Hiểu về semantic HTML là chìa khóa cho accessibility, SEO và maintainability.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>Sử dụng semantic elements cho ý nghĩa, không phải appearance</li>
                                <li>Cấu trúc nội dung với heading hierarchy phù hợp</li>
                                <li>Cung cấp alternative text cho images</li>
                                <li>Sử dụng forms đúng cách với proper labels</li>
                            </ul>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <span class="code-title">Semantic HTML Example</span>
                                <button class="copy-btn" data-clipboard="html-semantic">Copy</button>
                            </div>
                            <pre class="code-block" id="html-semantic"><code class="language-html"><!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Semantic HTML Example</title>
</head>
<body>
  <header>
    <nav aria-label="Main navigation">
      <ul>
        <li><a href="#home">Trang chủ</a></li>
        <li><a href="#about">Giới thiệu</a></li>
      </ul>
    </nav>
  </header>
  <main>
    <article>
      <h1>Tiêu đề bài viết</h1>
      <p>Nội dung bài viết...</p>
    </article>
  </main>
  <footer>
    <p>&copy; 2025 Example Company</p>
  </footer>
</body>
</html></code></pre>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="css-fundamentals">
                    <div class="subsection-header">
                        <h3>CSS Box Model & Cascade</h3>
                        <button class="bookmark-btn" data-bookmark="css-fundamentals">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>CSS Box Model là nền tảng cho layout. Mọi element đều là một box với content, padding, border và margin.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>Hiểu rõ content-box vs border-box sizing</li>
                                <li>Thành thạo cascade và specificity rules</li>
                                <li>Sử dụng CSS custom properties cho maintainability</li>
                                <li>Áp dụng modern layout methods (Flexbox, Grid)</li>
                            </ul>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <span class="code-title">CSS Box Model Example</span>
                                <button class="copy-btn" data-clipboard="css-box-model">Copy</button>
                            </div>
                            <pre class="code-block" id="css-box-model"><code class="language-css">/* Box Model Example */
.box {
  width: 300px;
  padding: 20px;
  border: 2px solid #333;
  margin: 10px;
  box-sizing: border-box; /* Include padding/border in width */
}

/* CSS Custom Properties */
:root {
  --primary-color: #007bff;
  --spacing-unit: 1rem;
}

.component {
  color: var(--primary-color);
  margin: var(--spacing-unit);
}</code></pre>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="javascript-core">
                    <div class="subsection-header">
                        <h3>JavaScript Fundamentals</h3>
                        <button class="bookmark-btn" data-bookmark="javascript-core">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Modern JavaScript (ES6+) cung cấp powerful features cho frontend development. Hiểu về closures, async programming và modules là thiết yếu.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>Sử dụng const/let thay vì var</li>
                                <li>Hiểu this binding và arrow functions</li>
                                <li>Thành thạo async/await cho asynchronous operations</li>
                                <li>Sử dụng modules cho code organization</li>
                            </ul>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <span class="code-title">Modern JavaScript Examples</span>
                                <button class="copy-btn" data-clipboard="js-modern">Copy</button>
                            </div>
                            <pre class="code-block" id="js-modern"><code class="language-javascript">// Modern JavaScript Examples

// Destructuring và default parameters
const processUser = ({ name, email, role = 'user' }) => {
  return { name, email, role };
};

// Async/await
const fetchUserData = async (userId) => {
  try {
    const response = await fetch(`/api/users/${userId}`);
    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Failed to fetch user:', error);
    throw error;
  }
};

// Modules
export { processUser, fetchUserData };</code></pre>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="dom-manipulation">
                    <div class="subsection-header">
                        <h3>DOM Manipulation</h3>
                        <button class="bookmark-btn" data-bookmark="dom-manipulation">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Hiểu về DOM là crucial cho frontend development. Học cách manipulate elements hiệu quả và handle events properly.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>Query elements hiệu quả với modern selectors</li>
                                <li>Sử dụng event delegation cho dynamic content</li>
                                <li>Minimize DOM operations cho better performance</li>
                                <li>Hiểu event bubbling và capturing</li>
                            </ul>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <span class="code-title">Modern DOM Manipulation</span>
                                <button class="copy-btn" data-clipboard="dom-modern">Copy</button>
                            </div>
                            <pre class="code-block" id="dom-modern"><code class="language-javascript">// Modern DOM Manipulation

// Query elements
const button = document.querySelector('.submit-btn');
const form = document.getElementById('contact-form');

// Event delegation
document.addEventListener('click', (event) => {
  if (event.target.matches('.dynamic-button')) {
    handleDynamicClick(event.target);
  }
});

// Create elements efficiently
const createUserCard = (user) => {
  const card = document.createElement('div');
  card.className = 'user-card';
  card.innerHTML = `
    <h3>${user.name}</h3>
    <p>${user.email}</p>
  `;
  return card;
};</code></pre>
                        </div>
                    </div>
                </article>
            </section>

            <!-- Core Principles Section -->
            <section class="content-section" id="core-principles">
                <div class="section-header">
                    <h2 class="section-title">⚡ Core Principles</h2>
                    <p class="section-description">Những nguyên tắc cốt lõi không thay đổi theo thời gian</p>
                </div>

                <article class="subsection" id="separation-concerns">
                    <div class="subsection-header">
                        <h3>Separation of Concerns</h3>
                        <button class="bookmark-btn" data-bookmark="separation-concerns">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Tách biệt HTML (structure), CSS (presentation), và JavaScript (behavior) để code maintainable.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>HTML chỉ cho content và structure</li>
                                <li>CSS cho visual presentation</li>
                                <li>JavaScript cho interactive behavior</li>
                                <li>Tránh inline styles và scripts</li>
                            </ul>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="progressive-enhancement">
                    <div class="subsection-header">
                        <h3>Progressive Enhancement</h3>
                        <button class="bookmark-btn" data-bookmark="progressive-enhancement">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Build core functionality trước, sau đó enhance với advanced features. Đảm bảo graceful degradation.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>Bắt đầu với semantic HTML</li>
                                <li>Thêm CSS cho visual enhancement</li>
                                <li>Sử dụng JavaScript để add interactivity</li>
                                <li>Test mà không có JavaScript enabled</li>
                            </ul>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="accessibility">
                    <div class="subsection-header">
                        <h3>Web Accessibility (WCAG)</h3>
                        <button class="bookmark-btn" data-bookmark="accessibility">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Làm cho web content accessible cho tất cả users, bao gồm những người khuyết tật.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc WCAG:</h4>
                            <ul>
                                <li>Perceivable: Cung cấp text alternatives, captions</li>
                                <li>Operable: Keyboard accessible, không có seizure triggers</li>
                                <li>Understandable: Readable text, predictable functionality</li>
                                <li>Robust: Compatible với assistive technologies</li>
                            </ul>
                        </div>
                    </div>
                </article>
            </section>

            <!-- Modern Development Section -->
            <section class="content-section" id="modern-development">
                <div class="section-header">
                    <h2 class="section-title">🚀 Modern Development</h2>
                    <p class="section-description">React, Next.js và ecosystem công nghệ hiện đại</p>
                </div>

                <article class="subsection" id="react-fundamentals">
                    <div class="subsection-header">
                        <h3>React Concepts & Hooks</h3>
                        <button class="bookmark-btn" data-bookmark="react-fundamentals">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>React đã cách mạng hóa frontend development với components và hooks. Master những patterns cơ bản.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>Components là building blocks</li>
                                <li>Props flow down, events flow up</li>
                                <li>Sử dụng hooks cho state và side effects</li>
                                <li>Giữ components focused và reusable</li>
                            </ul>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <span class="code-title">React Functional Component với Hooks</span>
                                <button class="copy-btn" data-clipboard="react-hooks">Copy</button>
                            </div>
                            <pre class="code-block" id="react-hooks"><code class="language-javascript">// React Functional Component với Hooks
import { useState, useEffect } from 'react';

const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        const userData = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) return <div>Đang tải...</div>;
  if (error) return <div>Lỗi: {error}</div>;
  if (!user) return <div>Không tìm thấy user</div>;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
};</code></pre>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="nextjs-concepts">
                    <div class="subsection-header">
                        <h3>Next.js Architecture</h3>
                        <button class="bookmark-btn" data-bookmark="nextjs-concepts">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Next.js cung cấp powerful features cho production React applications bao gồm SSR, SSG, và routing.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>File-based routing system</li>
                                <li>Static generation cho better performance</li>
                                <li>Server-side rendering khi cần</li>
                                <li>API routes cho backend functionality</li>
                            </ul>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <span class="code-title">Next.js Page với Static Generation</span>
                                <button class="copy-btn" data-clipboard="nextjs-static">Copy</button>
                            </div>
                            <pre class="code-block" id="nextjs-static"><code class="language-typescript">// Next.js Page với Static Generation
import { GetStaticProps, GetStaticPaths } from 'next';

interface Post {
  id: string;
  title: string;
  content: string;
}

interface Props {
  post: Post;
}

const PostPage = ({ post }: Props) => {
  return (
    <article>
      <h1>{post.title}</h1>
      <div>{post.content}</div>
    </article>
  );
};

// Static Site Generation
export const getStaticProps: GetStaticProps = async ({ params }) => {
  const post = await fetchPost(params?.id as string);
  return {
    props: { post },
    revalidate: 60, // ISR: revalidate mỗi phút
  };
};

export const getStaticPaths: GetStaticPaths = async () => {
  const posts = await fetchAllPosts();
  const paths = posts.map((post) => ({ params: { id: post.id } }));
  return { paths, fallback: 'blocking' };
};

export default PostPage;</code></pre>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="typescript-integration">
                    <div class="subsection-header">
                        <h3>TypeScript Benefits</h3>
                        <button class="bookmark-btn" data-bookmark="typescript-integration">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>TypeScript thêm static typing vào JavaScript, catching errors sớm và improving developer experience.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc bất biến:</h4>
                            <ul>
                                <li>Define interfaces cho data structures</li>
                                <li>Sử dụng type annotations cho function parameters</li>
                                <li>Leverage IDE support cho autocomplete</li>
                                <li>Gradually adopt trong existing projects</li>
                            </ul>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <span class="code-title">TypeScript Interface và Component</span>
                                <button class="copy-btn" data-clipboard="typescript-interface">Copy</button>
                            </div>
                            <pre class="code-block" id="typescript-interface"><code class="language-typescript">// TypeScript Interface và Component
interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'moderator';
}

interface UserListProps {
  users: User[];
  onUserSelect: (user: User) => void;
}

const UserList: React.FC<UserListProps> = ({ users, onUserSelect }) => {
  return (
    <ul className="user-list">
      {users.map((user) => (
        <li key={user.id} onClick={() => onUserSelect(user)}>
          <span>{user.name}</span>
          <span className={`role role--${user.role}`}>{user.role}</span>
        </li>
      ))}
    </ul>
  );
};</code></pre>
                        </div>
                    </div>
                </article>
            </section>

            <!-- Additional sections would continue here with similar structure -->
            <!-- Architecture & Patterns -->
            <section class="content-section" id="architecture-patterns">
                <div class="section-header">
                    <h2 class="section-title">🏛️ Architecture & Patterns</h2>
                    <p class="section-description">Design patterns và clean architecture cho code maintainable</p>
                </div>

                <article class="subsection" id="solid-principles">
                    <div class="subsection-header">
                        <h3>SOLID Principles</h3>
                        <button class="bookmark-btn" data-bookmark="solid-principles">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Áp dụng SOLID principles vào frontend code để có better maintainability và scalability.</p>
                        
                        <div class="principles-box">
                            <h4>5 Nguyên tắc SOLID:</h4>
                            <ul>
                                <li><strong>Single Responsibility:</strong> Một class chỉ có một lý do để thay đổi</li>
                                <li><strong>Open/Closed:</strong> Mở cho extension, đóng cho modification</li>
                                <li><strong>Liskov Substitution:</strong> Subtypes phải thay thế được cho base types</li>
                                <li><strong>Interface Segregation:</strong> Nhiều interfaces cụ thể thay vì một interface tổng quát</li>
                                <li><strong>Dependency Inversion:</strong> Phụ thuộc vào abstractions, không phải concretions</li>
                            </ul>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="component-patterns">
                    <div class="subsection-header">
                        <h3>Component Design Patterns</h3>
                        <button class="bookmark-btn" data-bookmark="component-patterns">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Học các patterns phổ biến để build reusable và maintainable components.</p>
                        
                        <div class="principles-box">
                            <h4>Patterns cốt lõi:</h4>
                            <ul>
                                <li>Container vs Presentational components</li>
                                <li>Higher-Order Components (HOCs)</li>
                                <li>Render props pattern</li>
                                <li>Custom hooks cho logic reuse</li>
                            </ul>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="state-management">
                    <div class="subsection-header">
                        <h3>State Management Patterns</h3>
                        <button class="bookmark-btn" data-bookmark="state-management">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Chọn đúng state management approach cho application complexity.</p>
                        
                        <div class="principles-box">
                            <h4>Lựa chọn phù hợp:</h4>
                            <ul>
                                <li>Local state với useState cho simple cases</li>
                                <li>Context API cho moderate sharing needs</li>
                                <li>Redux/Zustand cho complex global state</li>
                                <li>Server state với React Query/SWR</li>
                            </ul>
                        </div>
                    </div>
                </article>
            </section>

            <!-- Performance Section -->
            <section class="content-section" id="performance">
                <div class="section-header">
                    <h2 class="section-title">⚡ Performance & Optimization</h2>
                    <p class="section-description">Tối ưu performance và user experience</p>
                </div>

                <article class="subsection" id="rendering-optimization">
                    <div class="subsection-header">
                        <h3>Rendering Optimization</h3>
                        <button class="bookmark-btn" data-bookmark="rendering-optimization">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Hiểu cách browsers render pages và optimize critical rendering path.</p>
                        
                        <div class="principles-box">
                            <h4>Nguyên tắc tối ưu:</h4>
                            <ul>
                                <li>Minimize render-blocking resources</li>
                                <li>Optimize CSS delivery</li>
                                <li>Sử dụng resource hints (preload, prefetch)</li>
                                <li>Implement lazy loading cho images</li>
                            </ul>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="javascript-optimization">
                    <div class="subsection-header">
                        <h3>JavaScript Optimization</h3>
                        <button class="bookmark-btn" data-bookmark="javascript-optimization">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Tối ưu JavaScript cho better performance và user experience.</p>
                        
                        <div class="principles-box">
                            <h4>Kỹ thuật tối ưu:</h4>
                            <ul>
                                <li>Code splitting để reduce bundle size</li>
                                <li>Tree shaking để eliminate dead code</li>
                                <li>Sử dụng Web Workers cho heavy computations</li>
                                <li>Implement proper caching strategies</li>
                            </ul>
                        </div>
                    </div>
                </article>

                <article class="subsection" id="core-web-vitals">
                    <div class="subsection-header">
                        <h3>Core Web Vitals</h3>
                        <button class="bookmark-btn" data-bookmark="core-web-vitals">🔖</button>
                    </div>
                    <div class="content-body">
                        <p>Monitor và optimize cho Google's Core Web Vitals metrics.</p>
                        
                        <div class="principles-box">
                            <h4>3 Metrics quan trọng:</h4>
                            <ul>
                                <li><strong>LCP:</strong> Largest Contentful Paint - tối ưu loading performance</li>
                                <li><strong>FID:</strong> First Input Delay - minimize interaction delay</li>
                                <li><strong>CLS:</strong> Cumulative Layout Shift - prevent layout shifts</li>
                                <li>Sử dụng performance monitoring tools</li>
                            </ul>
                        </div>
                    </div>
                </article>
            </section>
        </main>
    </div>

    <!-- Floating Action Button for bookmarks -->
    <button class="fab" id="bookmarksToggle" aria-label="Xem bookmarks">
        <span class="fab-icon">📚</span>
    </button>

    <!-- Bookmarks Panel -->
    <div class="bookmarks-panel" id="bookmarksPanel">
        <div class="bookmarks-header">
            <h3>Bookmarks của bạn</h3>
            <button class="close-btn" id="closeBookmarks">&times;</button>
        </div>
        <div class="bookmarks-content" id="bookmarksContent">
            <p class="empty-bookmarks">Chưa có bookmarks nào. Hãy bookmark những phần quan trọng!</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.28.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="app.js"></script>
</body>
</html>