// Frontend Engineering Master Handbook - Interactive Application

class HandbookApp {
  constructor() {
    this.bookmarks = new Set();
    this.currentTheme = 'light';
    this.searchData = [];
    
    this.initializeApp();
    this.setupEventListeners();
    this.loadContent();
    this.setupSearch();
  }

  initializeApp() {
    // Detect system theme preference
    if (globalThis.matchMedia && globalThis.matchMedia('(prefers-color-scheme: dark)').matches) {
      this.currentTheme = 'dark';
      document.documentElement.setAttribute('data-color-scheme', 'dark');
    }
    this.updateThemeIcon();

    // Initialize progress bar
    this.updateProgressBar();
    
    // Initialize navigation
    this.highlightCurrentSection();
    
    // Setup Prism.js for syntax highlighting
    if (globalThis.Prism) {
      Prism.highlightAll();
    }
  }

  setupEventListeners() {
    // Theme toggle
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleTheme();
      });
    }

    // Sidebar toggle for mobile
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleSidebar();
      });
    }

    // Navigation buttons
    document.querySelectorAll('.nav-button').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleNavSection(e.target);
      });
    });

    // Navigation links
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', (e) => this.handleNavClick(e));
    });

    // Bookmark buttons
    document.querySelectorAll('.bookmark-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleBookmark(e.target);
      });
    });

    // Copy code buttons
    document.querySelectorAll('.copy-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        this.copyCode(e.target);
      });
    });

    // Bookmarks panel
    const bookmarksToggle = document.getElementById('bookmarksToggle');
    const closeBookmarks = document.getElementById('closeBookmarks');
    
    if (bookmarksToggle) {
      bookmarksToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleBookmarksPanel();
      });
    }
    
    if (closeBookmarks) {
      closeBookmarks.addEventListener('click', (e) => {
        e.preventDefault();
        this.closeBookmarksPanel();
      });
    }

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.handleSearch(e.target.value);
      });
      searchInput.addEventListener('focus', () => {
        if (searchInput.value.trim()) {
          this.showSearchResults();
        }
      });
      searchInput.addEventListener('blur', () => {
        // Delay hiding to allow clicking on results
        setTimeout(() => this.hideSearchResults(), 200);
      });
      searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          const firstResult = document.querySelector('.search-result-item.focused') || 
                              document.querySelector('.search-result-item');
          if (firstResult) {
            firstResult.click();
          }
        }
      });
    }

    // Scroll events for progress and navigation
    globalThis.addEventListener('scroll', this.throttle(() => {
      this.updateProgressBar();
      this.highlightCurrentSection();
    }, 100));

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', (e) => {
      const sidebar = document.getElementById('sidebar');
      const sidebarToggle = document.getElementById('sidebarToggle');
      
      if (globalThis.innerWidth <= 1024 && 
          sidebar && 
          !sidebar.contains(e.target) && 
          !sidebarToggle.contains(e.target)) {
        sidebar.classList.remove('open');
      }
    });

    // Handle escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeBookmarksPanel();
        this.hideSearchResults();
        
        if (globalThis.innerWidth <= 1024) {
          document.getElementById('sidebar')?.classList.remove('open');
        }
      }
    });

    // Handle window resize
    globalThis.addEventListener('resize', () => {
      if (globalThis.innerWidth > 1024) {
        document.getElementById('sidebar')?.classList.remove('open');
      }
    });

    // Listen for theme changes from parent window
    globalThis.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'THEME_CHANGE') {
        const theme = event.data.theme;
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-color-scheme', theme);
        this.updateThemeIcon();
      }
    });
  }

  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-color-scheme', this.currentTheme);
    this.updateThemeIcon();
    this.syncThemeWithParent(this.currentTheme);
    console.log('Theme changed to:', this.currentTheme);
  }

  updateThemeIcon() {
    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      themeIcon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
    }
  }

  syncThemeWithParent(theme) {
    if (globalThis.parent && globalThis.parent !== globalThis) {
      try {
        globalThis.parent.postMessage({
          type: 'THEME_CHANGE',
          theme: theme
        }, '*');
      } catch (_e) {
        // Ignore cross-origin errors
      }
    }
  }

  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
      sidebar.classList.toggle('open');
    }
  }

  toggleNavSection(button) {
    const isExpanded = button.classList.contains('expanded');
    const submenu = button.parentElement.querySelector('.nav-submenu');
    
    if (isExpanded) {
      button.classList.remove('expanded');
      submenu?.classList.remove('expanded');
    } else {
      // Close other expanded sections
      document.querySelectorAll('.nav-button.expanded').forEach(btn => {
        if (btn !== button) {
          btn.classList.remove('expanded');
          btn.parentElement.querySelector('.nav-submenu')?.classList.remove('expanded');
        }
      });
      
      button.classList.add('expanded');
      submenu?.classList.add('expanded');
    }
  }

  handleNavClick(e) {
    e.preventDefault();
    const href = e.target.getAttribute('href');
    
    if (href && href.startsWith('#')) {
      const targetId = href.substring(1);
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
          link.classList.remove('active');
        });
        e.target.classList.add('active');
        
        // Close sidebar on mobile
        if (globalThis.innerWidth <= 1024) {
          document.getElementById('sidebar')?.classList.remove('open');
        }
      }
    }
  }

  toggleBookmark(button) {
    const bookmarkId = button.getAttribute('data-bookmark');
    
    if (this.bookmarks.has(bookmarkId)) {
      this.bookmarks.delete(bookmarkId);
      button.classList.remove('bookmarked');
      button.setAttribute('aria-label', 'Bookmark section');
    } else {
      this.bookmarks.add(bookmarkId);
      button.classList.add('bookmarked');
      button.setAttribute('aria-label', 'Remove bookmark');
    }
    
    this.updateBookmarksPanel();
    console.log('Bookmarks updated:', Array.from(this.bookmarks));
  }

  updateBookmarksPanel() {
    const bookmarksContent = document.getElementById('bookmarksContent');
    if (!bookmarksContent) return;

    if (this.bookmarks.size === 0) {
      bookmarksContent.innerHTML = '<p class="empty-bookmarks">Chưa có bookmarks nào. Hãy bookmark những phần quan trọng!</p>';
      return;
    }

    let bookmarksHTML = '';
    
    this.bookmarks.forEach(bookmarkId => {
      const element = document.getElementById(bookmarkId);
      if (element) {
        const title = element.querySelector('h3')?.textContent || bookmarkId;
        const sectionTitle = element.closest('.content-section')?.querySelector('.section-title')?.textContent || 'Unknown Section';
        
        bookmarksHTML += `
          <a href="#${bookmarkId}" class="bookmark-item" onclick="app.handleBookmarkClick('${bookmarkId}'); return false;">
            <div class="bookmark-title">${title}</div>
            <div class="bookmark-section">${sectionTitle}</div>
          </a>
        `;
      }
    });
    
    bookmarksContent.innerHTML = bookmarksHTML;
  }

  handleBookmarkClick(bookmarkId) {
    const element = document.getElementById(bookmarkId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      this.closeBookmarksPanel();
    }
  }

  toggleBookmarksPanel() {
    const panel = document.getElementById('bookmarksPanel');
    if (panel) {
      const isOpen = panel.classList.contains('open');
      if (isOpen) {
        panel.classList.remove('open');
      } else {
        panel.classList.add('open');
        this.updateBookmarksPanel(); // Refresh bookmarks when opening
      }
      console.log('Bookmarks panel toggled:', !isOpen);
    }
  }

  closeBookmarksPanel() {
    const panel = document.getElementById('bookmarksPanel');
    if (panel) {
      panel.classList.remove('open');
    }
  }

  async copyCode(button) {
    const clipboardId = button.getAttribute('data-clipboard');
    const codeBlock = document.getElementById(clipboardId);
    
    if (codeBlock) {
      try {
        const code = codeBlock.textContent;
        await navigator.clipboard.writeText(code);
        
        button.textContent = 'Copied!';
        button.classList.add('copied');
        
        setTimeout(() => {
          button.textContent = 'Copy';
          button.classList.remove('copied');
        }, 2000);
      } catch (err) {
        console.error('Failed to copy code:', err);
        button.textContent = 'Error';
        
        setTimeout(() => {
          button.textContent = 'Copy';
        }, 2000);
      }
    }
  }

  updateProgressBar() {
    const scrollTop = globalThis.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - globalThis.innerHeight;
    const progress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
    
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
      progressFill.style.width = `${Math.min(Math.max(progress, 0), 100)}%`;
    }
  }

  highlightCurrentSection() {
    const sections = document.querySelectorAll('.subsection[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let currentSection = null;
    
    sections.forEach(section => {
      const rect = section.getBoundingClientRect();
      if (rect.top <= 200 && rect.bottom >= 200) {
        currentSection = section.id;
      }
    });
    
    if (currentSection) {
      navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === `#${currentSection}`) {
          link.classList.add('active');
        } else {
          link.classList.remove('active');
        }
      });
    }
  }

  loadContent() {
    // Content is already loaded in HTML
    this.updateBookmarksPanel();
  }

  setupSearch() {
    // Build search index from content
    this.buildSearchIndex();
  }

  buildSearchIndex() {
    const sections = document.querySelectorAll('.subsection');
    this.searchData = []; // Reset search data
    
    sections.forEach(section => {
      const id = section.id;
      const title = section.querySelector('h3')?.textContent || '';
      const content = section.querySelector('.content-body')?.textContent || '';
      const sectionTitle = section.closest('.content-section')?.querySelector('.section-title')?.textContent || '';
      
      if (id && title) {
        this.searchData.push({
          id,
          title,
          content: content.substring(0, 200), // First 200 characters for snippet
          section: sectionTitle,
          searchText: `${title} ${content} ${sectionTitle}`.toLowerCase()
        });
      }
    });
    
    console.log('Search index built with', this.searchData.length, 'items');
  }

  handleSearch(query) {
    if (!query.trim()) {
      this.hideSearchResults();
      return;
    }

    const results = this.searchData.filter(item => 
      item.searchText.includes(query.toLowerCase())
    ).slice(0, 5); // Limit to 5 results

    console.log('Search query:', query, 'Results:', results.length);
    this.displaySearchResults(results, query);
  }

  displaySearchResults(results, query) {
    const searchResults = document.getElementById('searchResults');
    if (!searchResults) {
      console.error('Search results container not found');
      return;
    }

    if (results.length === 0) {
      searchResults.innerHTML = '<div class="search-result-item">Không tìm thấy kết quả nào.</div>';
    } else {
      searchResults.innerHTML = results.map(result => `
        <div class="search-result-item" onclick="app.navigateToSection('${result.id}')">
          <div class="search-result-title">${this.highlightQuery(result.title, query)}</div>
          <div class="search-result-snippet">${this.highlightQuery(result.content, query)}...</div>
        </div>
      `).join('');
    }

    searchResults.classList.add('show');
    console.log('Search results displayed');
  }

  highlightQuery(text, query) {
    if (!text || !query) return text || '';
    const regex = new RegExp(`(${this.escapeRegExp(query)})`, 'gi');
    return text.replace(regex, '<strong>$1</strong>');
  }

  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  showSearchResults() {
    const searchResults = document.getElementById('searchResults');
    const searchInput = document.getElementById('searchInput');
    
    if (searchResults && searchInput && searchInput.value.trim()) {
      searchResults.classList.add('show');
    }
  }

  hideSearchResults() {
    const searchResults = document.getElementById('searchResults');
    if (searchResults) {
      searchResults.classList.remove('show');
    }
  }

  navigateToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      
      // Update navigation
      document.querySelectorAll('.nav-link').forEach(link => {
        const href = link.getAttribute('href');
        if (href === `#${sectionId}`) {
          link.classList.add('active');
        } else {
          link.classList.remove('active');
        }
      });
      
      this.hideSearchResults();
      const searchInput = document.getElementById('searchInput');
      if (searchInput) {
        searchInput.blur();
      }
      
      // Close sidebar on mobile
      if (globalThis.innerWidth <= 1024) {
        document.getElementById('sidebar')?.classList.remove('open');
      }
    }
  }

  // Utility methods
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  }

  // Accessibility helpers
  announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
}

// Additional utility functions for enhanced interactivity

// Code syntax highlighting enhancement
function enhanceSyntaxHighlighting() {
  if (globalThis.Prism) {
    // Re-highlight all code blocks
    Prism.highlightAll();
  }
}

// Keyboard navigation support
function setupKeyboardNavigation() {
  document.addEventListener('keydown', (e) => {
    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'k':
          e.preventDefault();
          document.getElementById('searchInput')?.focus();
          break;
        case 'b':
          e.preventDefault();
          if (globalThis.app) {
            globalThis.app.toggleBookmarksPanel();
          }
          break;
        case '/':
          e.preventDefault();
          document.getElementById('searchInput')?.focus();
          break;
      }
    }
    
    // Navigate search results with arrow keys
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    
    if (document.activeElement === searchInput && searchResults?.classList.contains('show')) {
      const resultItems = searchResults.querySelectorAll('.search-result-item');
      let focusedIndex = -1;
      
      resultItems.forEach((item, index) => {
        if (item.classList.contains('focused')) {
          focusedIndex = index;
        }
      });
      
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          focusedIndex = Math.min(focusedIndex + 1, resultItems.length - 1);
          break;
        case 'ArrowUp':
          e.preventDefault();
          focusedIndex = Math.max(focusedIndex - 1, -1);
          break;
        case 'Enter':
          if (focusedIndex >= 0) {
            e.preventDefault();
            resultItems[focusedIndex].click();
          }
          break;
      }
      
      // Update focused item
      resultItems.forEach((item, index) => {
        if (index === focusedIndex) {
          item.classList.add('focused');
        } else {
          item.classList.remove('focused');
        }
      });
    }
  });
}

// Print-friendly version
function setupPrintMode() {
  const mediaQueryList = globalThis.matchMedia('print');
  
  mediaQueryList.addListener((mql) => {
    if (mql.matches) {
      // Expand all collapsed sections for printing
      document.querySelectorAll('.nav-button:not(.expanded)').forEach(button => {
        button.classList.add('expanded');
        const submenu = button.parentElement.querySelector('.nav-submenu');
        submenu?.classList.add('expanded');
      });
    }
  });
}

// Initialize the application
let app;

document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing app...');
  app = new HandbookApp();
  globalThis.app = app; // Make app globally accessible
  
  // Setup additional features
  enhanceSyntaxHighlighting();
  setupKeyboardNavigation();
  setupPrintMode();
  
  // Add loading complete event
  globalThis.addEventListener('load', () => {
    document.body.classList.add('loaded');
    if (app && app.announceToScreenReader) {
      app.announceToScreenReader('Frontend Engineering Handbook đã tải xong');
    }
    console.log('Application fully loaded');
  });
});

// Ensure global access
globalThis.app = app;