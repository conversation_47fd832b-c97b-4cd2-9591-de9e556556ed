// Software Architecture Recovery & Reverse Engineering Handbook
// Interactive functionality

class HandbookApp {
    constructor() {
        this.currentSection = 'overview';
        this.searchIndex = [];
        this.theme = this.getStoredTheme();
        this.init();
    }

    init() {
        this.initializeTheme();
        this.setupNavigation();
        this.setupSearch();
        this.setupThemeToggle();
        this.setupCopyButtons();
        this.setupKeyboardNavigation();
        this.setupMobileMenu();
        this.buildSearchIndex();
        this.setupProgressIndicator();
        
        // Initialize syntax highlighting if Prism is available
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
    }

    // Theme Management
    getStoredTheme() {
        try {
            const stored = localStorage.getItem('handbook-theme');
            if (stored) return stored;
        } catch (e) {
            // LocalStorage not available, use default
        }
        
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }

    initializeTheme() {
        document.documentElement.setAttribute('data-color-scheme', this.theme);
        this.updateThemeIcon();
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                this.theme = event.data.theme;
                document.documentElement.setAttribute('data-color-scheme', this.theme);
                this.updateThemeIcon();
                try {
                    localStorage.setItem('handbook-theme', this.theme);
                } catch (e) {
                    // LocalStorage not available
                }
            }
        });
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-color-scheme', this.theme);
        
        try {
            localStorage.setItem('handbook-theme', this.theme);
        } catch (e) {
            // LocalStorage not available
        }
        
        this.updateThemeIcon();
        
        // Broadcast theme change to parent (unified toolkit)
        if (window.parent !== window) {
            try {
                window.parent.postMessage({
                    type: 'THEME_CHANGE',
                    theme: this.theme
                }, '*');
            } catch (e) {
                // Ignore cross-origin errors
            }
        }
    }

    updateThemeIcon() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.textContent = this.theme === 'dark' ? '☀️' : '🌙';
            themeToggle.setAttribute('title', `Switch to ${this.theme === 'dark' ? 'light' : 'dark'} mode`);
        }
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleTheme();
            });
        }
    }

    // Navigation Management
    setupNavigation() {
        const navButtons = document.querySelectorAll('.nav-button');
        navButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const sectionId = e.currentTarget.getAttribute('data-section');
                if (sectionId) {
                    this.navigateToSection(sectionId);
                }
            });
        });

        // Handle browser back/forward
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.section) {
                this.navigateToSection(e.state.section, false);
            }
        });
    }

    navigateToSection(sectionId, pushState = true) {
        // Map section names to actual IDs
        const sectionMap = {
            'overview': 'overview',
            'linux-commands': 'linux-commands',
            'static-analysis': 'static-analysis',
            'database-analysis': 'database-analysis',
            'documentation-generation': 'documentation-generation', 
            'graph-generation': 'graph-generation',
            'comprehensive-script': 'comprehensive-script',
            'professional-tools': 'professional-tools',
            'workflows': 'workflows',
            'framework-architecture': 'framework-architecture'
        };

        const actualSectionId = sectionMap[sectionId] || sectionId;

        // Hide current section
        const currentSection = document.querySelector('.content-section.active');
        if (currentSection) {
            currentSection.classList.remove('active');
        }

        // Show new section
        const newSection = document.getElementById(actualSectionId);
        if (newSection) {
            newSection.classList.add('active');
        } else {
            console.warn(`Section not found: ${actualSectionId}`);
            return;
        }

        // Update navigation
        document.querySelectorAll('.nav-button').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeNavButton = document.querySelector(`[data-section="${sectionId}"]`);
        if (activeNavButton) {
            activeNavButton.classList.add('active');
        }

        this.currentSection = actualSectionId;

        // Update URL and history
        if (pushState) {
            try {
                const url = new URL(window.location);
                url.searchParams.set('section', sectionId);
                history.pushState({ section: sectionId }, '', url);
            } catch (e) {
                // URL manipulation not available
            }
        }

        // Update progress
        this.updateProgress();

        // Scroll to top of content
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.scrollTop = 0;
        }
    }

    // Search Functionality
    buildSearchIndex() {
        const sections = document.querySelectorAll('.content-section');
        this.searchIndex = [];

        sections.forEach(section => {
            const sectionId = section.id;
            const sectionTitle = section.querySelector('h2')?.textContent || '';
            
            // Index headings
            const headings = section.querySelectorAll('h3, h4, h5');
            headings.forEach(heading => {
                this.searchIndex.push({
                    section: sectionId,
                    title: sectionTitle,
                    heading: heading.textContent,
                    content: heading.textContent,
                    element: heading,
                    type: 'heading'
                });
            });

            // Index paragraphs
            const paragraphs = section.querySelectorAll('p');
            paragraphs.forEach(p => {
                if (p.textContent.trim()) {
                    this.searchIndex.push({
                        section: sectionId,
                        title: sectionTitle,
                        content: p.textContent,
                        element: p,
                        type: 'text'
                    });
                }
            });

            // Index code blocks
            const codeBlocks = section.querySelectorAll('code');
            codeBlocks.forEach(code => {
                this.searchIndex.push({
                    section: sectionId,
                    title: sectionTitle,
                    content: code.textContent,
                    element: code.closest('.code-block') || code,
                    type: 'code'
                });
            });

            // Index list items
            const listItems = section.querySelectorAll('li');
            listItems.forEach(li => {
                if (li.textContent.trim()) {
                    this.searchIndex.push({
                        section: sectionId,
                        title: sectionTitle,
                        content: li.textContent,  
                        element: li,
                        type: 'list'
                    });
                }
            });
        });

        console.log(`Built search index with ${this.searchIndex.length} items`);
    }

    setupSearch() {
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');

        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });

            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch(e.target.value);
                }
            });
        }

        if (searchBtn) {
            searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                const query = searchInput ? searchInput.value : '';
                this.performSearch(query);
            });
        }
    }

    performSearch(query) {
        console.log(`Searching for: "${query}"`);
        
        // Clear previous highlights
        this.clearSearchHighlights();

        if (!query.trim()) {
            return;
        }

        const results = this.searchIndex.filter(item => 
            item.content.toLowerCase().includes(query.toLowerCase())
        );

        console.log(`Found ${results.length} search results`);

        if (results.length > 0) {
            // Navigate to first result's section
            const firstResult = results[0];
            
            // Map section IDs for navigation
            const sectionMap = {
                'overview': 'overview',
                'linux-commands': 'linux-commands', 
                'static-analysis': 'static-analysis',
                'database-analysis': 'database-analysis',
                'documentation-generation': 'documentation-generation',
                'graph-generation': 'graph-generation', 
                'comprehensive-script': 'comprehensive-script',
                'professional-tools': 'professional-tools',
                'workflows': 'workflows',
                'framework-architecture': 'framework-architecture'
            };

            const navSectionId = Object.keys(sectionMap).find(key => 
                sectionMap[key] === firstResult.section
            ) || firstResult.section;

            this.navigateToSection(navSectionId);

            // Highlight results with a delay to ensure section is loaded
            setTimeout(() => {
                this.highlightSearchResults(query, results);
            }, 200);
        } else {
            // Show no results message
            this.showNoResultsMessage(query);
        }
    }

    showNoResultsMessage(query) {
        // Create temporary message
        const message = document.createElement('div');
        message.className = 'search-no-results';
        message.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: var(--color-warning);
            color: var(--color-btn-primary-text);
            padding: var(--space-12) var(--space-16);
            border-radius: var(--radius-base);
            z-index: 1000;
            box-shadow: var(--shadow-lg);
        `;
        message.textContent = `No results found for "${query}"`;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    }

    highlightSearchResults(query, results) {
        const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi');

        results.forEach(result => {
            if (result.element && result.element.offsetParent !== null) {
                const element = result.element;
                
                // For code blocks, highlight differently
                if (result.type === 'code') {
                    element.style.background = 'rgba(var(--color-warning-rgb), 0.2)';
                    element.style.border = '2px solid var(--color-warning)';
                } else {
                    const textNodes = this.getTextNodes(element);

                    textNodes.forEach(textNode => {
                        if (textNode.textContent.toLowerCase().includes(query.toLowerCase())) {
                            const parent = textNode.parentNode;
                            const wrapper = document.createElement('span');
                            wrapper.innerHTML = textNode.textContent.replace(regex, '<span class="search-highlight">$1</span>');
                            
                            // Replace text node with highlighted content
                            const fragment = document.createDocumentFragment();
                            while (wrapper.firstChild) {
                                fragment.appendChild(wrapper.firstChild);
                            }
                            parent.replaceChild(fragment, textNode);
                        }
                    });
                }
            }
        });
    }

    clearSearchHighlights() {
        // Clear text highlights
        const highlights = document.querySelectorAll('.search-highlight');
        highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });

        // Clear code block highlights
        const codeBlocks = document.querySelectorAll('code, .code-block');
        codeBlocks.forEach(block => {
            block.style.background = '';
            block.style.border = '';
        });
    }

    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    // Skip code blocks and already highlighted content
                    if (node.parentNode.tagName === 'CODE' || 
                        node.parentNode.classList.contains('search-highlight')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    return node.textContent.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
                }
            }
        );

        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        return textNodes;
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Copy to Clipboard Functionality
    setupCopyButtons() {
        const copyButtons = document.querySelectorAll('.copy-btn');
        copyButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.copyToClipboard(e.currentTarget);
            });
        });
    }

    async copyToClipboard(button) {
        const text = button.getAttribute('data-clipboard-text');
        
        try {
            await navigator.clipboard.writeText(text);
            this.showCopySuccess(button);
        } catch (err) {
            // Fallback for older browsers
            this.fallbackCopyToClipboard(text, button);
        }
    }

    fallbackCopyToClipboard(text, button) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showCopySuccess(button);
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }

        document.body.removeChild(textArea);
    }

    showCopySuccess(button) {
        const originalText = button.textContent;
        button.textContent = '✓';
        button.classList.add('copied');

        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('copied');
        }, 2000);
    }

    // Keyboard Navigation
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Skip if user is typing in an input
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            switch (e.key) {
                case '/':
                    e.preventDefault();
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.focus();
                    }
                    break;
                case 'Escape':
                    this.clearSearchHighlights();
                    const searchField = document.getElementById('search-input');
                    if (searchField) {
                        searchField.value = '';
                    }
                    break;
                case 'ArrowUp':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.navigateToPreviousSection();
                    }
                    break;
                case 'ArrowDown':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.navigateToNextSection();
                    }
                    break;
            }
        });
    }

    navigateToPreviousSection() {
        const sections = ['overview', 'linux-commands', 'static-analysis', 'database-analysis', 
                         'documentation-generation', 'graph-generation', 'comprehensive-script', 
                         'professional-tools', 'workflows', 'framework-architecture'];
        const currentIndex = sections.indexOf(this.currentSection);
        if (currentIndex > 0) {
            this.navigateToSection(sections[currentIndex - 1]);
        }
    }

    navigateToNextSection() {
        const sections = ['overview', 'linux-commands', 'static-analysis', 'database-analysis', 
                         'documentation-generation', 'graph-generation', 'comprehensive-script', 
                         'professional-tools', 'workflows', 'framework-architecture'];
        const currentIndex = sections.indexOf(this.currentSection);
        if (currentIndex < sections.length - 1) {
            this.navigateToSection(sections[currentIndex + 1]);
        }
    }

    // Mobile Menu
    setupMobileMenu() {
        if (window.innerWidth <= 768) {
            this.createMobileMenuToggle();
        }

        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                this.createMobileMenuToggle();
            } else {
                this.removeMobileMenuToggle();
            }
        });
    }

    createMobileMenuToggle() {
        if (document.querySelector('.mobile-menu-toggle')) return;

        const toggle = document.createElement('button');
        toggle.className = 'mobile-menu-toggle';
        toggle.innerHTML = '☰';
        toggle.style.cssText = `
            display: block;
            background: var(--color-primary);
            color: var(--color-btn-primary-text);
            border: none;
            padding: var(--space-8);
            border-radius: var(--radius-base);
            font-size: var(--font-size-lg);
            cursor: pointer;
            position: fixed;
            top: var(--space-16);
            left: var(--space-16);
            z-index: 1001;
        `;

        toggle.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            sidebar.style.display = sidebar.style.display === 'none' ? 'block' : 'none';
        });

        document.body.appendChild(toggle);

        // Hide sidebar by default on mobile
        const sidebar = document.querySelector('.sidebar');
        sidebar.style.display = 'none';
    }

    removeMobileMenuToggle() {
        const toggle = document.querySelector('.mobile-menu-toggle');
        if (toggle) {
            toggle.remove();
        }

        const sidebar = document.querySelector('.sidebar');
        sidebar.style.display = 'block';
    }

    // Progress Indicator
    setupProgressIndicator() {
        // Create progress bar
        const progressIndicator = document.createElement('div');
        progressIndicator.className = 'progress-indicator';
        progressIndicator.innerHTML = '<div class="progress-bar"></div>';
        document.body.appendChild(progressIndicator);

        this.updateProgress();
    }

    updateProgress() {
        const sections = ['overview', 'linux-commands', 'static-analysis', 'database-analysis', 
                         'documentation-generation', 'graph-generation', 'comprehensive-script', 
                         'professional-tools', 'workflows'];
        const currentIndex = sections.indexOf(this.currentSection);
        const progress = currentIndex >= 0 ? ((currentIndex + 1) / sections.length) * 100 : 0;

        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }

    // Handle URL parameters on page load
    handleInitialLoad() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const section = urlParams.get('section');
            
            if (section && document.getElementById(section)) {
                this.navigateToSection(section, false);
            }
        } catch (e) {
            // URL manipulation not available
        }
    }

    // Initialize tooltips
    setupTooltips() {
        const elements = document.querySelectorAll('[title]');
        elements.forEach(element => {
            const title = element.getAttribute('title');
            if (title) {
                element.classList.add('tooltip');
                element.setAttribute('data-tooltip', title);
                element.removeAttribute('title');
            }
        });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new HandbookApp();
    
    // Handle initial URL parameters
    app.handleInitialLoad();
    
    // Setup tooltips
    app.setupTooltips();
    
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add keyboard shortcut hints
    const keyboardHints = document.createElement('div');
    keyboardHints.style.cssText = `
        position: fixed;
        bottom: var(--space-16);
        right: var(--space-16);
        background: var(--color-surface);
        border: 1px solid var(--color-border);
        border-radius: var(--radius-base);
        padding: var(--space-8);
        font-size: var(--font-size-xs);
        color: var(--color-text-secondary);
        z-index: 1000;
        opacity: 0.7;
        max-width: 200px;
    `;
    keyboardHints.innerHTML = `
        <div><strong>Phím tắt:</strong></div>
        <div>/ - Tìm kiếm</div>
        <div>Ctrl+↑/↓ - Chuyển section</div>
        <div>Esc - Xóa tìm kiếm</div>
    `;
    document.body.appendChild(keyboardHints);

    // Hide keyboard hints after 5 seconds
    setTimeout(() => {
        keyboardHints.style.opacity = '0';
        setTimeout(() => {
            keyboardHints.remove();
        }, 300);
    }, 5000);

    console.log('🔧 Software Architecture Recovery & Reverse Engineering Handbook loaded successfully!');
});