<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Software Architecture Recovery & Reverse Engineering Handbook</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-okaidia.min.css" media="(prefers-color-scheme: dark)">
</head>
<body>
    <div class="handbook-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="header-title">
                    <span class="title-icon">🔧</span>
                    Software Architecture Recovery & Reverse Engineering
                </h1>
                <div class="header-controls">
                    <div class="search-container">
                        <input type="text" id="search-input" class="search-input" placeholder="Tìm kiếm trong handbook...">
                        <button class="search-btn" id="search-btn">🔍</button>
                    </div>
                    <button class="theme-toggle" id="theme-toggle" title="Toggle theme">🌙</button>
                </div>
            </div>
        </header>

        <div class="main-layout">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
                <nav class="sidebar-nav">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <button class="nav-button active" data-section="overview">
                                <span class="nav-icon">📖</span>
                                Overview & Core Concepts
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="linux-commands">
                                <span class="nav-icon">💻</span>
                                Linux Commands & System Analysis
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="static-analysis">
                                <span class="nav-icon">🔍</span>
                                Static Code Analysis Tools
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="database-analysis">
                                <span class="nav-icon">🗄️</span>
                                Database Schema Analysis
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="documentation-generation">
                                <span class="nav-icon">📄</span>
                                Automated Documentation Generation
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="graph-generation">
                                <span class="nav-icon">📊</span>
                                Graph Generation & Visualization
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="comprehensive-script">
                                <span class="nav-icon">⚙️</span>
                                Comprehensive Analysis Scripts
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="professional-tools">
                                <span class="nav-icon">🛠️</span>
                                Professional Tools
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="workflows">
                                <span class="nav-icon">📋</span>
                                Workflows & Best Practices
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-button" data-section="framework-architecture">
                                <span class="nav-icon">🏗️</span>
                                Framework Architecture
                            </button>
                        </li>
                    </ul>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Overview Section -->
                <section class="content-section active" id="overview">
                    <h2>Overview & Core Concepts</h2>
                    <div class="section-intro">
                        <p>Software Architecture Recovery và Reverse Engineering Documentation là lĩnh vực chuyên môn cao trong kỹ thuật phần mềm, đòi hỏi sự kết hợp giữa Static Code Analysis, Linux System Administration, Graph Theory và Documentation Automation.</p>
                    </div>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Kỹ năng cốt lõi</h3>
                            <ul class="skill-list">
                                <li><strong>Static Code Analysis</strong> - Phân tích tĩnh mã nguồn</li>
                                <li><strong>Linux System Administration</strong> - Command-line mastery</li>
                                <li><strong>Graph Theory</strong> - Visualization techniques</li>
                                <li><strong>Documentation Automation</strong> - Tự động hóa tài liệu</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Mindset "Khảo cổ phần mềm"</h3>
                            <p>Chuyển đổi tư duy phát triển phần mềm truyền thống (code-first → design) thành một tư duy maps-first → develop dựa trên việc mô hình hóa và vẽ bản đồ phần mềm toàn diện.</p>
                        </div>
                    </div>
                </section>
                
                <!-- Framework Architecture Section -->
                <section class="content-section" id="framework-architecture">
                    <h2>Mastering Framework Architecture: Universal OOP & Design Pattern Analysis</h2>
                    <div class="section-intro">
                        <p>Hiểu được cách phân tích và "đảo ngược" kiến trúc của bất kỳ framework nào là một kỹ năng cốt lõi của một developer giỏi. Từ một keyword đơn lẻ, bạn có thể khám phá toàn bộ luồng hoạt động, thiết kế pattern và kiến trúc của hệ thống. Đây là hướng dẫn toàn diện về phương pháp luận này.</p>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Quy Trình Phân Tích Framework Phổ Quát</h3>
                            <h4>Bước 1: Tìm Kiếm Từ Khóa Ban Đầu (Initial Reconnaissance)</h4>
                            <p><strong>Nguyên tắc cơ bản:</strong> Bắt đầu với một từ khóa duy nhất và tìm kiếm toàn cục trong toàn bộ codebase<sup>[1][2]</sup>.</p>
                            <div class="code-block">
                                <pre><code class="language-bash"># Lệnh tìm kiếm phổ quát
grep -R -n "keyword" . --exclude-dir=node_modules --exclude-dir=vendor
rg "keyword" --type js --type php --type ts</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -R -n "keyword" . --exclude-dir=node_modules --exclude-dir=vendor&#10;rg "keyword" --type js --type php --type ts'>📋</button>
                            </div>
                            <p><strong>Ví dụ với <code>adonis-auditing</code>:</strong></p>
                            <ul>
                                <li>Tìm kiếm <code>@stouder-io/adonis-auditing</code> trong toàn bộ project</li>
                                <li>Kết quả cho thấy xuất hiện ở: <code>package.json</code>, <code>config/auditing.ts</code>, <code>app/models/app_model.ts</code></li>
                            </ul>

                            <h4>Bước 2: Phân Loại Vai Trò File (File Role Classification)</h4>
                            <p>Mỗi file tìm được có một <strong>vai trò cụ thể</strong> trong kiến trúc<sup>[3][4]</sup>:</p>
                            <ul>
                                <li><strong>WordPress:</strong>
                                    <ul>
                                        <li><code>functions.php</code> → Theme hooks and customizations</li>
                                        <li><code>wp-config.php</code> → Configuration entry point</li>
                                        <li>Plugin files → Feature implementations</li>
                                    </ul>
                                </li>
                                <li><strong>NestJS:</strong>
                                    <ul>
                                        <li><code>main.ts</code> → Application bootstrap</li>
                                        <li><code>*.module.ts</code> → Module definitions and dependency injection</li>
                                        <li><code>*.controller.ts</code> → Request handlers</li>
                                    </ul>
                                </li>
                                <li><strong>AdonisJS:</strong>
                                    <ul>
                                        <li><code>adonisrc.ts</code> → Framework configuration</li>
                                        <li><code>app/Controllers/</code> → MVC controllers</li>
                                        <li><code>config/</code> → Service configurations</li>
                                    </ul>
                                </li>
                                <li><strong>React/Next.js:</strong>
                                    <ul>
                                        <li><code>_app.js</code> → Application wrapper</li>
                                        <li><code>pages/</code> → Route definitions</li>
                                        <li><code>components/</code> → Reusable UI elements</li>
                                    </ul>
                                </li>
                            </ul>

                            <h4>Bước 3: Nhận Diện Design Patterns</h4>
                            <p>Framework hiện đại đều sử dụng các design patterns phổ biến, nhưng cách implementation khác nhau<sup>[5][6]</sup>:</p>
                            <p><strong>Singleton Pattern:</strong></p>
                            <ul>
                                <li>WordPress: <code>$wpdb</code> global object, <code>wp_cache</code></li>
                                <li>NestJS: <code>@Injectable()</code> services với singleton scope</li>
                                <li>React: Context API, Redux store</li>
                            </ul>
                            <p><strong>Observer Pattern:</strong></p>
                            <ul>
                                <li>WordPress: Hook system (<code>add_action</code>, <code>add_filter</code>)</li>
                                <li>NestJS: Event emitters, <code>@OnEvent</code> decorators</li>
                                <li>React: <code>useEffect</code>, component lifecycle</li>
                            </ul>
                            <p><strong>Dependency Injection:</strong></p>
                            <ul>
                                <li>WordPress: Plugin dependency management</li>
                                <li>NestJS: Constructor injection, providers array</li>
                                <li>AdonisJS: IoC Container, <code>@inject</code> decorator</li>
                            </ul>

                            <h4>Bước 4: Truy Vết Dependency và Data Flow</h4>
                            <p><strong>Phân tích import/require chains:</strong></p>
                            <div class="code-block">
                                <pre><code class="language-bash"># Tìm tất cả import statements
grep -R "import\|require\|use" --include="*.js" --include="*.ts" --include="*.php" .

# Phân tích dependency tree
npm ls --depth=3
composer show --tree</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -R "import\|require\|use" --include="*.js" --include="*.ts" --include="*.php" .&#10;npm ls --depth=3&#10;composer show --tree'>📋</button>
                            </div>

                            <p><strong>WordPress Hook Tracing:</strong></p>
                            <div class="code-block">
                                <pre><code class="language-bash"># Tìm hook registrations
grep -R "add_action\|add_filter" .
# Tìm hook executions  
grep -R "do_action\|apply_filters" .</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -R "add_action\|add_filter" .&#10;grep -R "do_action\|apply_filters" .'>📋</button>
                            </div>

                            <p><strong>NestJS Module Analysis:</strong></p>
                            <div class="code-block">
                                <pre><code class="language-bash"># Tìm module imports
grep -R "@Module" --include="*.ts" .
# Tìm provider registrations
grep -R "providers:\|imports:" --include="*.ts" .</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -R "@Module" --include="*.ts" .&#10;grep -R "providers:\|imports:" --include="*.ts" .'>📋</button>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Framework-Specific Investigation Techniques</h3>
                            <h4>WordPress OOP Architecture</h4>
                            <p>WordPress sử dụng <strong>Hook-based Plugin Architecture</strong> kết hợp với OOP<sup>[7][8]</sup>:</p>
                            <p><strong>Core Patterns:</strong></p>
                            <ul>
                                <li><strong>Action/Filter System:</strong> Observer pattern implementation</li>
                                <li><strong>Plugin Architecture:</strong> Modular design with hooks</li>
                                <li><strong>Template Hierarchy:</strong> Strategy pattern for theme rendering</li>
                            </ul>
                            <p><strong>Investigation Commands:</strong></p>
                            <div class="code-block">
                                <pre><code class="language-bash"># Tìm plugin structure
find wp-content/plugins -name "*.php" | head -20
# Analyze hook usage
grep -R "add_action\|add_filter" wp-content/
# Template hierarchy analysis  
find wp-content/themes -name "*.php" | grep -E "(index|single|page|archive)"</code></pre>
                                <button class="copy-btn" data-clipboard-text='find wp-content/plugins -name "*.php" | head -20&#10;grep -R "add_action\|add_filter" wp-content/&#10;find wp-content/themes -name "*.php" | grep -E "(index|single|page|archive)"'>📋</button>
                            </div>

                            <h4>NestJS Modular Architecture</h4>
                            <p>NestJS sử dụng <strong>Angular-inspired modular design</strong> với heavy decoration pattern<sup>[5][9]</sup>:</p>
                            <p><strong>Core Patterns:</strong></p>
                            <ul>
                                <li><strong>Dependency Injection:</strong> IoC container pattern</li>
                                <li><strong>Decorator Pattern:</strong> Extensive use of TypeScript decorators</li>
                                <li><strong>Module Pattern:</strong> Feature-based code organization</li>
                            </ul>
                            <p><strong>Investigation Commands:</strong></p>
                            <div class="code-block">
                                <pre><code class="language-bash"># Find decorators usage
grep -R "@Injectable\|@Controller\|@Module" --include="*.ts" .
# Analyze dependency injection
grep -R "constructor(" --include="*.ts" .
# Module structure analysis
find src -name "*.module.ts" | xargs grep -l "imports:\|providers:"</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -R "@Injectable\|@Controller\|@Module" --include="*.ts" .&#10;grep -R "constructor(" --include="*.ts" .&#10;find src -name "*.module.ts" | xargs grep -l "imports:\|providers:"'>📋</button>
                            </div>

                            <h4>AdonisJS MVC Framework</h4>
                            <p>AdonisJS theo <strong>traditional MVC pattern</strong> với modern TypeScript features<sup>[2]</sup>.</p>
                            <p><strong>Core Patterns:</strong></p>
                            <ul>
                                <li><strong>MVC Architecture:</strong> Clear separation of concerns</li>
                                <li><strong>Service Container:</strong> Dependency injection container</li>
                                <li><strong>Provider Pattern:</strong> Service registration and bootstrapping</li>
                            </ul>

                            <h4>React/Next.js Component Architecture</h4>
                            <p>React ecosystem sử dụng <strong>Component-based architecture</strong> với functional programming principles<sup>[10][11]</sup>:</p>
                            <p><strong>Core Patterns:</strong></p>
                            <ul>
                                <li><strong>Component Tree:</strong> Composite pattern</li>
                                <li><strong>Hooks Pattern:</strong> State management abstraction  </li>
                                <li><strong>HOC/Render Props:</strong> Decorator and Strategy patterns</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Universal Analysis Methodology</h3>
                            <h4>Phase 1: Reconnaissance (Trinh Sát)</h4>
                            <p><strong>Objective:</strong> Gather initial information about the codebase structure</p>
                            <p><strong>Techniques:</strong></p>
                            <div class="code-block">
                                <pre><code class="language-bash"># Global keyword search
grep -R "keyword" .
# File discovery
find . -name "pattern" -type f
# Directory analysis
tree -L 3 -I node_modules</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -R "keyword" .&#10;find . -name "pattern" -type f&#10;tree -L 3 -I node_modules'>📋</button>
                            </div>

                            <h4>Phase 2: Static Analysis (Phân Tích Tĩnh)</h4>
                            <p><strong>Objective:</strong> Understand code structure without execution</p>
                            <ul>
                                <li><strong>Tools:</strong> AST parsers, Linters, Dependency analyzers</li>
                            </ul>

                            <h4>Phase 3: Pattern Recognition (Nhận Diện Patterns)</h4>
                            <p><strong>Objective:</strong> Identify architectural and design patterns in use</p>
                            <ul>
                                <li><strong>Techniques:</strong> GoF detection, MVC/MVVM identification, framework conventions</li>
                            </ul>

                            <h4>Phase 4: Architecture Mapping (Lập Bản Đồ Kiến Trúc)</h4>
                            <p><strong>Objective:</strong> Create visual representation of system architecture</p>
                            <ul>
                                <li><strong>Deliverables:</strong> Component relationship diagrams, Service dependency graphs, Data flow documentation</li>
                            </ul>

                            <h4>Phase 5: Flow Reconstruction (Tái Tạo Luồng)</h4>
                            <p><strong>Objective:</strong> Understand complete workflow from input to output</p>
                            <ul>
                                <li><strong>Techniques:</strong> Sequence diagram creation, Request/response flow tracing, Event-driven flow mapping</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Practical Investigation Guide</h3>
                            <h4>WordPress Investigation Example</h4>
                            <div class="code-block">
                                <pre><code class="language-bash"># 1. Find plugin entry point
grep -R "Plugin Name:" wp-content/plugins/
# 2. Trace hook usage
grep -R "add_action.*init" wp-content/plugins/my-plugin/
# 3. Follow the execution chain
grep -R "do_action.*init" wp-includes/
# 4. Understand data flow
grep -R "apply_filters.*the_content" wp-includes/</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -R "Plugin Name:" wp-content/plugins/&#10;grep -R "add_action.*init" wp-content/plugins/my-plugin/&#10;grep -R "do_action.*init" wp-includes/&#10;grep -R "apply_filters.*the_content" wp-includes/'>📋</button>
                            </div>

                            <h4>NestJS Investigation Example</h4>
                            <div class="code-block">
                                <pre><code class="language-bash"># 1. Find application entry point
cat src/main.ts
# 2. Trace module imports
grep -R "@Module" src/ | grep -E "imports:|providers:"
# 3. Follow dependency injection
grep -R "constructor.*private" src/
# 4. Understand request lifecycle
grep -R "@Controller\|@Get\|@Post" src/</code></pre>
                                <button class="copy-btn" data-clipboard-text='cat src/main.ts&#10;grep -R "@Module" src/ | grep -E "imports:|providers:"&#10;grep -R "constructor.*private" src/&#10;grep -R "@Controller\|@Get\|@Post" src/'>📋</button>
                            </div>

                            <h4>Universal Principles</h4>
                            <ul>
                                <li><strong>Start with Keywords:</strong> Mọi framework đều có entry points và keywords đặc trưng</li>
                                <li><strong>Follow the Trail:</strong> Import/require statements là universal - follow chúng để hiểu dependencies</li>
                                <li><strong>Understand Patterns:</strong> Design patterns là language-agnostic, chỉ cách implement khác nhau</li>
                                <li><strong>Map Dependencies:</strong> Mọi framework đều có dependency management system</li>
                                <li><strong>Reconstruct Flow:</strong> Mọi framework đều có request/response hoặc event-driven cycle</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Advanced Techniques</h3>
                            <h4>Dynamic Analysis</h4>
                            <ul>
                                <li><strong>Debugger usage:</strong> Setting breakpoints and step-through execution</li>
                                <li><strong>Performance profiling:</strong> Understanding bottlenecks and hot paths</li>
                                <li><strong>Memory analysis:</strong> Object lifecycle and garbage collection</li>
                            </ul>

                            <h4>Automated Analysis Tools</h4>
                            <p><strong>Static Analysis:</strong> SonarQube, CodeClimate, Dependency-cruiser</p>
                            <p><strong>Framework-Specific Tools:</strong> WordPress: WP-CLI, Query Monitor; NestJS CLI; React DevTools</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Best Practices for Framework Mastery</h3>
                            <h4>Documentation Strategy</h4>
                            <ul>
                                <li><strong>Architecture Decision Records (ADRs):</strong> Document why certain patterns were chosen</li>
                                <li><strong>Sequence Diagrams:</strong> Visual representation of complex workflows</li>
                                <li><strong>Component Diagrams:</strong> Clear dependency relationships</li>
                                <li><strong>Data Flow Diagrams:</strong> Understanding information flow through system</li>
                            </ul>

                            <h4>Continuous Learning Approach</h4>
                            <ul>
                                <li><strong>Framework Source Code Reading:</strong> Study how frameworks implement core features</li>
                                <li><strong>Pattern Implementation Study:</strong> Compare how same patterns are implemented across frameworks</li>
                                <li><strong>Migration Case Studies:</strong> Understanding how to move between frameworks</li>
                                <li><strong>Performance Analysis:</strong> Benchmarking different architectural approaches</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Conclusion</h3>
                            <p>Mastering framework architecture analysis không chỉ giúp bạn debug hiệu quả mà còn cho phép bạn:</p>
                            <ul>
                                <li><strong>Customize frameworks</strong> một cách deep và professional</li>
                                <li><strong>Design better applications</strong> bằng cách hiểu trade-offs của từng pattern  </li>
                                <li><strong>Migrate between frameworks</strong> với sự hiểu biết sâu sắc</li>
                                <li><strong>Contribute to open source</strong> projects với confidence</li>
                            </ul>
                            <p>Quy trình này universal - áp dụng được từ legacy COBOL systems đến modern microservices architectures<sup>[12][13]</sup>. Key là áp dụng systematic approach và không ngừng practice với different codebases.</p>
                            <p>Framework architectures evolve, nhưng fundamental design patterns và investigation principles remain constant. Master được methodology này sẽ giúp bạn adapt nhanh chóng với bất kỳ technology stack nào trong tương lai.</p>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Sources</h3>
                            <ul>
                                <li><a href="https://www.geeksforgeeks.org/system-design/patterns-and-frameworks-in-ooad/" target="_blank" rel="noopener">[1] Patterns and Frameworks in OOAD - GeeksforGeeks</a></li>
                                <li><a href="https://reliasoftware.com/blog/leading-node-js-frameworks-for-web-application-development" target="_blank" rel="noopener">[2] Top 9 Best Node.js Frameworks For Web App Development</a></li>
                                <li><a href="https://www.c-sharpcorner.com/interview-question/what-is-a-difference-between-a-framework-an-design-pattern-and-an-architecture" target="_blank" rel="noopener">[3] Framework vs Pattern vs Architecture</a></li>
                                <li><a href="https://www.ulixenova.com/en/guida-ai-migliori-design-pattern-per-lo-sviluppo-front-end/" target="_blank" rel="noopener">[4] Guide to the Best Design Patterns for Front-End Development</a></li>
                                <li><a href="https://dev.to/amirtaherkhani/nestjs-design-patterns-23fc" target="_blank" rel="noopener">[5] NestJS Design Patterns - DEV Community</a></li>
                                <li><a href="https://refactoring.guru/design-patterns" target="_blank" rel="noopener">[6] Design Patterns - Refactoring.Guru</a></li>
                                <li><a href="https://kinsta.com/blog/wordpress-hooks/" target="_blank" rel="noopener">[7] WordPress Hooks</a></li>
                                <li><a href="https://learn.wordpress.org/tutorial/wordpress-filter-hooks/" target="_blank" rel="noopener">[8] WordPress Filter Hooks</a></li>
                                <li><a href="https://duypt.dev/cac-design-pattern-can-biet-khi-lam-viec-voi-nestjs" target="_blank" rel="noopener">[9] Các design pattern cần biết khi làm việc với NestJS</a></li>
                                <li><a href="https://stackademic.com/blog/5-design-patterns-for-building-scalable-next-js-applications" target="_blank" rel="noopener">[10] 5 Design Patterns for Building Scalable Next.js Applications</a></li>
                                <li><a href="https://dev.to/nithya_iyer/5-design-patterns-for-building-scalable-nextjs-applications-1c80" target="_blank" rel="noopener">[11] 5 Design Patterns for Building Scalable Next.js Applications</a></li>
                                <li><a href="https://swimm.io/learn/mainframe-modernization/reverse-engineering-in-software-engineering-process-and-best-practices" target="_blank" rel="noopener">[12] Reverse Engineering in Software Engineering: Process & Best Practices</a></li>
                                <li><a href="https://letsdefend.io/blog/top-7-reverse-engineering-tools" target="_blank" rel="noopener">[13] Top 7 Reverse Engineering Tools</a></li>
                                <!-- Additional references from [14]..[60] can be added as needed -->
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Linux Commands Section -->
                <section class="content-section" id="linux-commands">
                    <h2>Linux Commands & System Analysis</h2>
                    <div class="section-intro">
                        <p>Các lệnh Linux cơ bản để phân tích cấu trúc và dependency của dự án phần mềm.</p>
                    </div>

                    <div class="command-grid">
                        <div class="command-card">
                            <h3>Project Structure Analysis</h3>
                            <p>Tìm tất cả files trong project và phân tích cấu trúc</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find /path/to/project -type f -name "*.py" -o -name "*.js" -o -name "*.java" | wc -l</code></pre>
                                <button class="copy-btn" data-clipboard-text='find /path/to/project -type f -name "*.py" -o -name "*.js" -o -name "*.java" | wc -l'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Dependency Analysis</h3>
                            <p>Phân tích dependency bằng grep và xargs</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find . -name "*.py" | xargs grep -l "import\|from" | \
    xargs grep -H "import\|from" > dependencies_raw.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='find . -name "*.py" | xargs grep -l "import\|from" | \
    xargs grep -H "import\|from" > dependencies_raw.txt'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Database Connection Analysis</h3>
                            <p>Tìm tất cả database connections</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find . -type f \( -name "*.py" -o -name "*.js" \) | \
    xargs grep -l "connect\|query\|SELECT\|INSERT" > db_files.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='find . -type f \( -name "*.py" -o -name "*.js" \) | \
    xargs grep -l "connect\|query\|SELECT\|INSERT" > db_files.txt'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Function Mapping</h3>
                            <p>Phân tích function calls và relationships</p>
                            <div class="code-block">
                                <pre><code class="language-bash">find . -name "*.py" | xargs grep -n "def " | \
    sed 's/:def /|/' > functions_map.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='find . -name "*.py" | xargs grep -n "def " | \
    sed '"'"'s/:def /|/'"'"' > functions_map.txt'>📋</button>
                            </div>
                        </div>

                        <div class="command-card">
                            <h3>Dependency Matrix Creation</h3>
                            <p>Tạo dependency matrix từ source code</p>
                            <div class="code-block">
                                <pre><code class="language-bash">for file in $(find . -name "*.py"); do
  echo "=== $file ==="
  grep -n "import\|from" "$file" | \
    sed "s|^|$file:|"
done > full_dependency_map.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='for file in $(find . -name "*.py"); do
  echo "=== $file ==="
  grep -n "import\|from" "$file" | \
    sed "s|^|$file:|"
done > full_dependency_map.txt'>📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Static Analysis Section -->
                <section class="content-section" id="static-analysis">
                    <h2>Static Code Analysis Tools</h2>
                    
                    <div class="tool-grid">
                        <div class="tool-card">
                            <h3>Doxygen</h3>
                            <p class="tool-description">Tạo documentation từ comments</p>
                            <div class="tool-details">
                                <h4>Installation:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">sudo apt-get install doxygen graphviz</code></pre>
                                    <button class="copy-btn" data-clipboard-text="sudo apt-get install doxygen graphviz">📋</button>
                                </div>
                                <h4>Usage:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">doxygen -g config_file
sed -i 's/EXTRACT_ALL.*=.*NO/EXTRACT_ALL = YES/' config_file
sed -i 's/HAVE_DOT.*=.*NO/HAVE_DOT = YES/' config_file
doxygen config_file</code></pre>
                                    <button class="copy-btn" data-clipboard-text="doxygen -g config_file
sed -i 's/EXTRACT_ALL.*=.*NO/EXTRACT_ALL = YES/' config_file
sed -i 's/HAVE_DOT.*=.*NO/HAVE_DOT = YES/' config_file
doxygen config_file">📋</button>
                                </div>
                            </div>
                        </div>

                        <div class="tool-card">
                            <h3>Madge</h3>
                            <p class="tool-description">JavaScript dependency analysis</p>
                            <div class="tool-details">
                                <h4>Installation:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">npm install -g madge</code></pre>
                                    <button class="copy-btn" data-clipboard-text="npm install -g madge">📋</button>
                                </div>
                                <h4>Usage:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">madge --format es6 --image graph.svg src/</code></pre>
                                    <button class="copy-btn" data-clipboard-text="madge --format es6 --image graph.svg src/">📋</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Database Analysis Section -->
                <section class="content-section" id="database-analysis">
                    <h2>Database Schema Analysis</h2>
                    
                    <div class="technique-grid">
                        <div class="technique-card">
                            <h3>MySQL Schema Export</h3>
                            <p>Extract database schema từ MySQL</p>
                            <div class="code-block">
                                <pre><code class="language-bash">mysqldump -u user -p --no-data --routines database_name > schema.sql</code></pre>
                                <button class="copy-btn" data-clipboard-text="mysqldump -u user -p --no-data --routines database_name > schema.sql">📋</button>
                            </div>
                        </div>

                        <div class="technique-card">
                            <h3>Table Relationships Analysis</h3>
                            <p>Analyze tables and relationships</p>
                            <div class="code-block">
                                <pre><code class="language-bash">grep -E "(CREATE TABLE|FOREIGN KEY)" schema.sql | \
    sed 's/.*`\([^`]*\)`.*/\1/' > tables_relationships.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='grep -E "(CREATE TABLE|FOREIGN KEY)" schema.sql | \
    sed '"'"'s/.*`\([^`]*\)`.*/\1/'"'"' > tables_relationships.txt'>📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Documentation Generation Section -->
                <section class="content-section" id="documentation-generation">
                    <h2>Automated Documentation Generation</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Python Analyzer Script</h3>
                            <p>Script Python để tự động tạo documentation</p>
                            <div class="code-block large">
                                <pre><code class="language-python">import os
import ast
import json

def analyze_python_file(filepath):
    with open(filepath, 'r') as f:
        tree = ast.parse(f.read())
    
    analysis = {
        'classes': [],
        'functions': [],
        'imports': [],
        'docstrings': []
    }
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            analysis['classes'].append({
                'name': node.name,
                'lineno': node.lineno,
                'methods': [m.name for m in node.body if isinstance(m, ast.FunctionDef)]
            })
        elif isinstance(node, ast.FunctionDef):
            analysis['functions'].append({
                'name': node.name,
                'lineno': node.lineno,
                'args': [arg.arg for arg in node.args.args]
            })
    
    return analysis

# Analyze entire project
project_analysis = {}
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.py'):
            filepath = os.path.join(root, file)
            project_analysis[filepath] = analyze_python_file(filepath)

# Export to JSON for further processing
with open('project_analysis.json', 'w') as f:
    json.dump(project_analysis, f, indent=2)</code></pre>
                                <button class="copy-btn" data-clipboard-text="import os
import ast
import json

def analyze_python_file(filepath):
    with open(filepath, 'r') as f:
        tree = ast.parse(f.read())
    
    analysis = {
        'classes': [],
        'functions': [],
        'imports': [],
        'docstrings': []
    }
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            analysis['classes'].append({
                'name': node.name,
                'lineno': node.lineno,
                'methods': [m.name for m in node.body if isinstance(m, ast.FunctionDef)]
            })
        elif isinstance(node, ast.FunctionDef):
            analysis['functions'].append({
                'name': node.name,
                'lineno': node.lineno,
                'args': [arg.arg for arg in node.args.args]
            })
    
    return analysis

# Analyze entire project
project_analysis = {}
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.py'):
            filepath = os.path.join(root, file)
            project_analysis[filepath] = analyze_python_file(filepath)

# Export to JSON for further processing
with open('project_analysis.json', 'w') as f:
    json.dump(project_analysis, f, indent=2)">📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Graph Generation Section -->
                <section class="content-section" id="graph-generation">
                    <h2>Graph Generation & Visualization</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>GraphViz Dependency Script</h3>
                            <div class="code-block large">
                                <pre><code class="language-python">import os
import re
import sys

print("digraph dependencies {")
print("  rankdir=LR;")
print("  node [shape=box];")

# Analyze Python imports
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.py'):
            filepath = os.path.join(root, file)
            filename = file[:-3]  # Remove .py
            
            with open(filepath, 'r') as f:
                content = f.read()
                
            # Find imports
            imports = re.findall(r'from\s+(\w+)\s+import|import\s+(\w+)', content)
            for imp in imports:
                imported = imp[0] if imp[0] else imp[1]
                if not imported.startswith('_'):
                    print(f'  "{filename}" -> "{imported}";')

print("}")</code></pre>
                                <button class="copy-btn" data-clipboard-text='import os
import re
import sys

print("digraph dependencies {")
print("  rankdir=LR;")
print("  node [shape=box];")

# Analyze Python imports
for root, dirs, files in os.walk("."):
    for file in files:
        if file.endswith(".py"):
            filepath = os.path.join(root, file)
            filename = file[:-3]  # Remove .py
            
            with open(filepath, "r") as f:
                content = f.read()
                
            # Find imports
            imports = re.findall(r"from\s+(\w+)\s+import|import\s+(\w+)", content)
            for imp in imports:
                imported = imp[0] if imp[0] else imp[1]
                if not imported.startswith("_"):
                    print(f'"  {filename}" -> "{imported}";")

print("}")'>📋</button>
                            </div>
                            <div class="usage-steps">
                                <h4>Usage:</h4>
                                <div class="code-block">
                                    <pre><code class="language-bash">python3 generate_graph.py > project_deps.dot
dot -Tpng project_deps.dot -o dependency_graph.png</code></pre>
                                    <button class="copy-btn" data-clipboard-text="python3 generate_graph.py > project_deps.dot
dot -Tpng project_deps.dot -o dependency_graph.png">📋</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Comprehensive Script Section -->
                <section class="content-section" id="comprehensive-script">
                    <h2>Comprehensive Analysis Script</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Complete Reverse Engineering Script</h3>
                            <div class="code-block large">
                                <pre><code class="language-bash">#!/bin/bash
# comprehensive_analysis.sh - Complete reverse engineering script

PROJECT_PATH=${1:-.}
OUTPUT_DIR="analysis_output"

echo "Starting comprehensive source code analysis..."
mkdir -p $OUTPUT_DIR

# 1. Project structure analysis
echo "Analyzing project structure..."
find $PROJECT_PATH -type f | grep -E '\.(py|js|java|php|rb)$' > $OUTPUT_DIR/source_files.txt
tree $PROJECT_PATH -I '__pycache__|node_modules|.git' > $OUTPUT_DIR/project_tree.txt

# 2. Dependency analysis
echo "Extracting dependencies..."
find $PROJECT_PATH -name "*.py" | xargs grep -h "import\|from" | \
    sort | uniq > $OUTPUT_DIR/dependencies.txt

# 3. Database schema extraction
echo "Analyzing database connections..."
find $PROJECT_PATH -type f | xargs grep -l "CREATE TABLE\|SELECT\|INSERT\|UPDATE" | \
    head -10 > $OUTPUT_DIR/db_related_files.txt

# 4. Function and class mapping
echo "Mapping functions and classes..."
find $PROJECT_PATH -name "*.py" | xargs grep -n "^class\|^def " > $OUTPUT_DIR/functions_classes.txt

# 5. Configuration files
echo "Finding configuration files..."
find $PROJECT_PATH -name "*.conf" -o -name "*.ini" -o -name "*.yaml" -o -name "*.json" > $OUTPUT_DIR/config_files.txt</code></pre>
                                <button class="copy-btn" data-clipboard-text='#!/bin/bash
# comprehensive_analysis.sh - Complete reverse engineering script

PROJECT_PATH=${1:-.}
OUTPUT_DIR="analysis_output"

echo "Starting comprehensive source code analysis..."
mkdir -p $OUTPUT_DIR

# 1. Project structure analysis
echo "Analyzing project structure..."
find $PROJECT_PATH -type f | grep -E "\.(py|js|java|php|rb)$" > $OUTPUT_DIR/source_files.txt
tree $PROJECT_PATH -I "__pycache__|node_modules|.git" > $OUTPUT_DIR/project_tree.txt

# 2. Dependency analysis
echo "Extracting dependencies..."
find $PROJECT_PATH -name "*.py" | xargs grep -h "import\|from" | \
    sort | uniq > $OUTPUT_DIR/dependencies.txt

# 3. Database schema extraction
echo "Analyzing database connections..."
find $PROJECT_PATH -type f | xargs grep -l "CREATE TABLE\|SELECT\|INSERT\|UPDATE" | \
    head -10 > $OUTPUT_DIR/db_related_files.txt

# 4. Function and class mapping
echo "Mapping functions and classes..."
find $PROJECT_PATH -name "*.py" | xargs grep -n "^class\|^def " > $OUTPUT_DIR/functions_classes.txt

# 5. Configuration files
echo "Finding configuration files..."
find $PROJECT_PATH -name "*.conf" -o -name "*.ini" -o -name "*.yaml" -o -name "*.json" > $OUTPUT_DIR/config_files.txt'>📋</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Professional Tools Section -->
                <section class="content-section" id="professional-tools">
                    <h2>Professional Tools</h2>
                    
                    <div class="tools-section">
                        <h3>Enterprise Tools</h3>
                        <div class="tools-grid">
                            <div class="tool-item">
                                <h4>Imagix 4D</h4>
                                <p>Professional source code analysis</p>
                                <span class="status status--warning">Commercial</span>
                            </div>
                            <div class="tool-item">
                                <h4>Understand</h4>
                                <p>Code analysis và reverse engineering</p>
                                <span class="status status--warning">Commercial</span>
                            </div>
                            <div class="tool-item">
                                <h4>SourceTrail</h4>
                                <p>Interactive source explorer</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>Axivion</h4>
                                <p>Architecture verification</p>
                                <span class="status status--warning">Commercial</span>
                            </div>
                        </div>
                    </div>

                    <div class="tools-section">
                        <h3>Open Source Tools</h3>
                        <div class="tools-grid">
                            <div class="tool-item">
                                <h4>Sourcegraph</h4>
                                <p>Code search và navigation</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>Neo4j</h4>
                                <p>Graph database để lưu relationships</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>PlantUML</h4>
                                <p>Generate UML từ code</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                            <div class="tool-item">
                                <h4>Arcan</h4>
                                <p>Dependency analysis for Java</p>
                                <span class="status status--success">Open Source</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Workflows Section -->
                <section class="content-section" id="workflows">
                    <h2>Workflows & Best Practices</h2>
                    
                    <div class="card">
                        <div class="card__body">
                            <h3>Optimal Workflow</h3>
                            <div class="workflow-steps">
                                <div class="workflow-step">
                                    <h4>1. Discovery Phase</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">./comprehensive_analysis.sh /path/to/project</code></pre>
                                        <button class="copy-btn" data-clipboard-text="./comprehensive_analysis.sh /path/to/project">📋</button>
                                    </div>
                                </div>
                                <div class="workflow-step">
                                    <h4>2. Documentation Phase</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">doxygen config_file
sphinx-build -b html source build</code></pre>
                                        <button class="copy-btn" data-clipboard-text="doxygen config_file
sphinx-build -b html source build">📋</button>
                                    </div>
                                </div>
                                <div class="workflow-step">
                                    <h4>3. Graph Generation</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">madge --image graph.svg src/
dot -Tpng dependencies.dot -o arch_diagram.png</code></pre>
                                        <button class="copy-btn" data-clipboard-text="madge --image graph.svg src/
dot -Tpng dependencies.dot -o arch_diagram.png">📋</button>
                                    </div>
                                </div>
                                <div class="workflow-step">
                                    <h4>4. Final Integration</h4>
                                    <div class="code-block">
                                        <pre><code class="language-bash">pandoc analysis_report.md -o final_documentation.pdf</code></pre>
                                        <button class="copy-btn" data-clipboard-text="pandoc analysis_report.md -o final_documentation.pdf">📋</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card__body">
                            <h3>Ứng dụng thực tế</h3>
                            <ul class="application-list">
                                <li>Legacy system modernization</li>
                                <li>Technical debt assessment</li>
                                <li>Code review và architecture validation</li>
                                <li>Onboarding new team members</li>
                                <li>Compliance và audit requirements</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-bash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    <script src="app.js"></script>
</body>
</html>