<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Unified Thinking Toolkits</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="preload" as="document" href="../algorithm-thinking-toolkit/index.html">
  <link rel="preload" as="document" href="../software-refactoring-toolkit/index.html">
  <link rel="preload" as="document" href="../software-archaeology-handbook/index.html">
  <link rel="preload" as="document" href="../backend-toolkit-handbook/index.html">
  <link rel="preload" as="document" href="../master-any-skill-toolkit/index.html">
  <link rel="preload" as="document" href="../software-archaeology-toolkit/index.html">
  <link rel="preload" as="document" href="../software-toolkit-handbook/index.html">
  <link rel="preload" as="document" href="../golang-toolkit/index.html">
  <link rel="preload" as="document" href="../complete-linux-handbook/index.html">
  <link rel="preload" as="document" href="../software-architecture-explorer/index.html">
  <link rel="preload" as="document" href="../frontend-handbook/index.html">
  <link rel="preload" as="document" href="../comprehensive-mobile-handbook/index.html">
</head>
<body>
  <header class="header">
    <div class="container header__content">
      <div class="logo">
        <div class="logo__icon">🧰</div>
        <h1 class="logo__title">Unified Thinking Toolkits</h1>
      </div>
      <div class="header__actions">
        <button id="theme-toggle" class="btn btn--secondary" title="Toggle theme">🌙</button>
      </div>
    </div>
  </header>

  <main class="main">
    <div class="container">
      <nav class="tabs" role="tablist" aria-label="Toolkits">
        <button class="tab active" role="tab" aria-selected="true" data-toolkit="algorithm" id="tab-algorithm">Algorithm Thinking</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="creative" id="tab-creative">Creative Thinking</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="mst" id="tab-mst">MST Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="handbook" id="tab-handbook">SE Handbook</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="thinkingos" id="tab-thinkingos">Thinking OS</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="refactoring" id="tab-refactoring">Software Refactoring</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="archaeology" id="tab-archaeology">Software Archaeology</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="backend" id="tab-backend">Backend Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="master-skill" id="tab-master-skill">Master Any Skill</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="archaeology-toolkit" id="tab-archaeology-toolkit">Software Archaeology Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="software-toolkit" id="tab-software-toolkit">Software Toolkit</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="golang" id="tab-golang">Golang</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="linux-handbook" id="tab-linux-handbook">Linux Handbook</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="architecture-explorer" id="tab-architecture-explorer">Architecture Explorer</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="frontend-handbook" id="tab-frontend-handbook">Frontend Handbook</button>
        <button class="tab" role="tab" aria-selected="false" data-toolkit="mobile-handbook" id="tab-mobile-handbook">Mobile Handbook</button>
      </nav>

      <section class="frames">
        <iframe
          id="frame-algorithm"
          class="toolkit-frame"
          title="Algorithm Thinking Toolkit"
          data-src="../algorithm-thinking-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-creative"
          class="toolkit-frame hidden"
          title="Creative Thinking Toolkit"
          data-src="../creative-thinking-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-mst"
          class="toolkit-frame hidden"
          title="MST Toolkit"
          data-src="../mst-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-handbook"
          class="toolkit-frame hidden"
          title="Software Engineer Handbook"
          data-src="../software-engineer-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-thinkingos"
          class="toolkit-frame hidden"
          title="Thinking OS Toolkit"
          data-src="../thinking-os-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-refactoring"
          class="toolkit-frame hidden"
          title="Software Refactoring Toolkit"
          data-src="../software-refactoring-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-archaeology"
          class="toolkit-frame hidden"
          title="Software Archaeology Handbook"
          data-src="../software-archaeology-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-backend"
          class="toolkit-frame hidden"
          title="Backend Toolkit Handbook"
          data-src="../backend-toolkit-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-master-skill"
          class="toolkit-frame hidden"
          title="Master Any Skill Toolkit"
          data-src="../master-any-skill-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-archaeology-toolkit"
          class="toolkit-frame hidden"
          title="Software Archaeology Toolkit"
          data-src="../software-archaeology-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-software-toolkit"
          class="toolkit-frame hidden"
          title="Software Toolkit Handbook"
          data-src="../software-toolkit-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-golang"
          class="toolkit-frame hidden"
          title="Golang Toolkit"
          data-src="../golang-toolkit/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-linux-handbook"
          class="toolkit-frame hidden"
          title="Complete Linux Handbook"
          data-src="../complete-linux-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-architecture-explorer"
          class="toolkit-frame hidden"
          title="Software Architecture Explorer"
          data-src="../software-architecture-explorer/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-frontend-handbook"
          class="toolkit-frame hidden"
          title="Frontend Engineering Master Handbook"
          data-src="../frontend-handbook/index.html"
          loading="lazy"
        ></iframe>

        <iframe
          id="frame-mobile-handbook"
          class="toolkit-frame hidden"
          title="Comprehensive Mobile Engineering Handbook"
          data-src="../comprehensive-mobile-handbook/index.html"
          loading="lazy"
        ></iframe>
      </section>
    </div>
  </main>

  <script src="app.js"></script>
</body>
</html>


