(function () {
  function select(el, all = false) {
    return all ? document.querySelectorAll(el) : document.querySelector(el);
  }

  function ensureFrameLoaded(frame) {
    if (!frame) return;
    if (!frame.getAttribute('src')) {
      const src = frame.getAttribute('data-src');
      if (src) frame.setAttribute('src', src);
    }
  }

  function setFrameHeights() {
    const header = select('.header');
    const tabs = select('.tabs');
    const headerH = header ? header.offsetHeight : 0;
    const tabsH = tabs ? tabs.offsetHeight : 0;
    const padding = 48;
    const target = Math.max(320, window.innerHeight - headerH - tabsH - padding);
    select('.frames .toolkit-frame', true).forEach(f => {
      f.style.height = target + 'px';
    });
  }

  function debounce(fn, wait) {
    let t;
    return function () {
      clearTimeout(t);
      t = setTimeout(() => fn.apply(this, arguments), wait);
    };
  }

  function activateTab(toolkit) {
    // Tabs state
    select('.tab', true).forEach(tab => {
      const isActive = tab.getAttribute('data-toolkit') === toolkit;
      tab.classList.toggle('active', isActive);
      tab.setAttribute('aria-selected', String(isActive));
    });

    // Frames state
    select('.toolkit-frame', true).forEach(frame => {
      const idMatch = frame.id.replace('frame-', '') === toolkit;
      frame.classList.toggle('hidden', !idMatch);
      if (idMatch) ensureFrameLoaded(frame);
    });

    // Persist selection
    try { localStorage.setItem('unifiedToolkit.active', toolkit); } catch {}
  }

  function initTabs() {
    select('.tab', true).forEach(tab => {
      tab.addEventListener('click', () => activateTab(tab.getAttribute('data-toolkit')));
      tab.addEventListener('keydown', e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          activateTab(tab.getAttribute('data-toolkit'));
        }
      });
    });

    const saved = (function(){ try { return localStorage.getItem('unifiedToolkit.active'); } catch { return null; } })();
    activateTab(saved || 'algorithm');
  }

  function setTheme(theme) {
    document.documentElement.setAttribute('data-color-scheme', theme);
    const btn = select('#theme-toggle');
    if (btn) btn.textContent = theme === 'dark' ? '☀️' : '🌙';
    try { localStorage.setItem('unifiedToolkit.theme', theme); } catch {}
    
    // Broadcast theme change to all child toolkits
    const frames = select('.toolkit-frame', true);
    frames.forEach(frame => {
      if (frame.contentWindow) {
        try {
          frame.contentWindow.postMessage({
            type: 'THEME_CHANGE',
            theme: theme
          }, '*');
        } catch (e) {
          // Ignore cross-origin errors
        }
      }
    });
  }

  function initTheme() {
    let theme = 'light';
    try { theme = localStorage.getItem('unifiedToolkit.theme') || 'light'; } catch {}
    setTheme(theme);
    const toggle = select('#theme-toggle');
    if (toggle) {
      toggle.addEventListener('click', () => {
        const current = document.documentElement.getAttribute('data-color-scheme') || 'light';
        setTheme(current === 'dark' ? 'light' : 'dark');
      });
    }
    
    // Listen for theme changes from child toolkits
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'THEME_CHANGE') {
        setTheme(event.data.theme);
      }
    });
  }

  document.addEventListener('DOMContentLoaded', function () {
    initTheme();
    initTabs();
    ensureFrameLoaded(select('#frame-algorithm'));
    setFrameHeights();
    window.addEventListener('resize', debounce(setFrameHeights, 150));
  });
})();


