:root {
  --color-bg: #fcfcf9;
  --color-surface: #fffffd;
  --color-text: #13343b;
  --color-text-secondary: #626c71;
  --color-primary: #21808d;
  --color-primary-hover: #1d7480;
  --color-border: rgba(94, 82, 64, 0.2);
}

[data-color-scheme="dark"] {
  --color-bg: #1f2121;
  --color-surface: #262828;
  --color-text: #f5f5f5;
  --color-text-secondary: rgba(245,245,245,0.7);
  --color-primary: #32b8c6;
  --color-primary-hover: #2da6b2;
  --color-border: rgba(119,124,124,0.3);
}

html {
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
  color: var(--color-text);
  background: var(--color-bg);
}

body { margin: 0; }

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.logo { display: flex; align-items: center; gap: 12px; }
.logo__icon { font-size: 24px; background: rgba(6,182,212,0.08); padding: 8px; border-radius: 8px; }
.logo__title { margin: 0; font-size: 20px; color: var(--color-primary); }

.btn { cursor: pointer; border: 1px solid var(--color-border); background: var(--color-surface); color: var(--color-text); padding: 8px 12px; border-radius: 8px; }
.btn:hover { background: rgba(94,82,64,0.08); }

.main { padding: 24px 0; }

.tabs { display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 12px; }
.tab {
  padding: 8px 12px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  color: var(--color-text-secondary);
}
.tab:hover { color: var(--color-text); border-color: var(--color-primary); }
.tab.active { background: var(--color-primary); color: #fff; border-color: var(--color-primary); }

.frames { position: relative; }
.toolkit-frame { width: 100%; height: 70vh; border: 1px solid var(--color-border); border-radius: 10px; background: var(--color-surface); }
.hidden { display: none; }

@media (max-width: 768px) {
  .toolkit-frame { height: 65vh; }
}


