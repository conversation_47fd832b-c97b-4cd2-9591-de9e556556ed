<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Software Engineer Toolkit Handbook</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1 class="header-title">🚀 Software Engineer Toolkit</h1>
            <div class="header-actions">
                <div class="search-container">
                    <input type="text" id="searchInput" class="search-input" placeholder="Tìm kiếm công cụ, sách, khóa học...">
                    <button class="search-btn">🔍</button>
                </div>
                <button class="theme-toggle" id="themeToggle" title="Chuyển theme">🌙</button>
            </div>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" id="overallProgress"></div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3>Danh <PERSON>c</h3>
            <button class="sidebar-toggle" id="sidebarToggle">←</button>
        </div>
        <div class="nav-sections">
            <div class="nav-section">
                <button class="nav-section-btn active" data-section="dashboard">
                    📊 Tổng Quan
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="roadmap">
                    🗺️ Lộ Trình Học Tập
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="books">
                    📚 Sách Nền Tảng
                    <span class="progress-badge" id="books-progress">0%</span>
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="tools">
                    🛠️ Công Cụ Phát Triển
                    <span class="progress-badge" id="tools-progress">0%</span>
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="architecture">
                    🏗️ Kiến Trúc và Thiết Kế
                    <span class="progress-badge" id="architecture-progress">0%</span>
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="testing">
                    🧪 Testing & QA
                    <span class="progress-badge" id="testing-progress">0%</span>
                </button>
            </div>
            <div class="nav-section">
                <button class="nav-section-btn" data-section="ai-tools">
                    🤖 AI Tools & Modern Dev
                    <span class="progress-badge" id="ai-tools-progress">0%</span>
                </button>
            </div>
        </div>
        <div class="sidebar-footer">
            <button class="btn btn--secondary btn--sm" id="exportData">📤 Xuất Dữ Liệu</button>
            <button class="btn btn--outline btn--sm" id="resetProgress">🔄 Reset</button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="dashboard-header">
                <h2>Tổng Quan Tiến Độ Học Tập</h2>
                <p class="dashboard-subtitle">Theo dõi tiến độ học tập của bạn qua các lĩnh vực công nghệ</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📚</div>
                    <div class="stat-content">
                        <h3 id="totalItems">0</h3>
                        <p>Tổng Tài Nguyên</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3 id="completedItems">0</h3>
                        <p>Đã Hoàn Thành</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-content">
                        <h3 id="bookmarkedItems">0</h3>
                        <p>Đã Lưu</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-content">
                        <h3 id="overallProgressPercent">0%</h3>
                        <p>Tiến Độ Tổng</p>
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <h3>Hành Động Nhanh</h3>
                <div class="quick-action-buttons">
                    <button class="btn btn--primary" id="randomRecommendation">🎲 Gợi Ý Ngẫu Nhiên</button>
                    <button class="btn btn--secondary" id="showBookmarks">⭐ Xem Đã Lưu</button>
                    <button class="btn btn--outline" id="quickStart">🚀 Hướng Dẫn Nhanh</button>
                </div>
            </div>
        </section>

        <!-- Roadmap Section -->
        <section id="roadmap-section" class="content-section">
            <div class="section-header">
                <h2>🗺️ Lộ Trình Học Tập</h2>
                <p>Lộ trình toàn diện để trở thành Software Engineer chuyên nghiệp</p>
            </div>
            <div class="roadmap-container" id="roadmapContainer">
                <!-- Roadmap will be generated by JavaScript -->
            </div>
        </section>

        <!-- Category Sections -->
        <section id="books-section" class="content-section">
            <div class="section-header">
                <h2>📚 Sách Nền Tảng</h2>
                <p>Những cuốn sách thiết yếu cho mọi Software Engineer</p>
                <div class="section-filters">
                    <button class="filter-btn active" data-filter="all">Tất Cả</button>
                    <button class="filter-btn" data-filter="beginner">Cơ Bản</button>
                    <button class="filter-btn" data-filter="intermediate">Trung Cấp</button>
                    <button class="filter-btn" data-filter="advanced">Nâng Cao</button>
                    <button class="filter-btn" data-filter="essential">Thiết Yếu</button>
                </div>
            </div>
            <div class="items-grid" id="books-grid">
                <!-- Items will be generated by JavaScript -->
            </div>
        </section>

        <section id="tools-section" class="content-section">
            <div class="section-header">
                <h2>🛠️ Công Cụ Phát Triển</h2>
                <p>Các công cụ và nền tảng cần thiết cho development</p>
                <div class="section-filters">
                    <button class="filter-btn active" data-filter="all">Tất Cả</button>
                    <button class="filter-btn" data-filter="beginner">Cơ Bản</button>
                    <button class="filter-btn" data-filter="intermediate">Trung Cấp</button>
                    <button class="filter-btn" data-filter="advanced">Nâng Cao</button>
                    <button class="filter-btn" data-filter="essential">Thiết Yếu</button>
                </div>
            </div>
            <div class="items-grid" id="tools-grid">
                <!-- Items will be generated by JavaScript -->
            </div>
        </section>

        <section id="architecture-section" class="content-section">
            <div class="section-header">
                <h2>🏗️ Kiến Trúc và Thiết Kế</h2>
                <p>Patterns và principles cho system design</p>
                <div class="section-filters">
                    <button class="filter-btn active" data-filter="all">Tất Cả</button>
                    <button class="filter-btn" data-filter="beginner">Cơ Bản</button>
                    <button class="filter-btn" data-filter="intermediate">Trung Cấp</button>
                    <button class="filter-btn" data-filter="advanced">Nâng Cao</button>
                    <button class="filter-btn" data-filter="essential">Thiết Yếu</button>
                </div>
            </div>
            <div class="items-grid" id="architecture-grid">
                <!-- Items will be generated by JavaScript -->
            </div>
        </section>

        <section id="testing-section" class="content-section">
            <div class="section-header">
                <h2>🧪 Testing & QA</h2>
                <p>Methodologies và tools cho quality assurance</p>
                <div class="section-filters">
                    <button class="filter-btn active" data-filter="all">Tất Cả</button>
                    <button class="filter-btn" data-filter="beginner">Cơ Bản</button>
                    <button class="filter-btn" data-filter="intermediate">Trung Cấp</button>
                    <button class="filter-btn" data-filter="advanced">Nâng Cao</button>
                    <button class="filter-btn" data-filter="essential">Thiết Yếu</button>
                </div>
            </div>
            <div class="items-grid" id="testing-grid">
                <!-- Items will be generated by JavaScript -->
            </div>
        </section>

        <section id="ai-tools-section" class="content-section">
            <div class="section-header">
                <h2>🤖 AI Tools & Modern Dev</h2>
                <p>Công cụ AI và xu hướng development hiện đại</p>
                <div class="section-filters">
                    <button class="filter-btn active" data-filter="all">Tất Cả</button>
                    <button class="filter-btn" data-filter="beginner">Cơ Bản</button>
                    <button class="filter-btn" data-filter="intermediate">Trung Cấp</button>
                    <button class="filter-btn" data-filter="advanced">Nâng Cao</button>
                    <button class="filter-btn" data-filter="essential">Thiết Yếu</button>
                </div>
            </div>
            <div class="items-grid" id="ai-tools-grid">
                <!-- Items will be generated by JavaScript -->
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div class="modal hidden" id="quickStartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🚀 Hướng Dẫn Nhanh</h3>
                <button class="modal-close" data-modal="quickStartModal">×</button>
            </div>
            <div class="modal-body">
                <h4>Chào mừng đến với Software Engineer Toolkit!</h4>
                <ul>
                    <li><strong>Khám Phá:</strong> Duyệt qua các danh mục bên trái để tìm tài nguyên phù hợp</li>
                    <li><strong>Tìm Kiếm:</strong> Sử dụng thanh tìm kiếm để tìm nội dung cụ thể</li>
                    <li><strong>Theo Dõi:</strong> Tick vào các item để đánh dấu hoàn thành</li>
                    <li><strong>Lưu Trữ:</strong> Click vào ⭐ để bookmark những tài nguyên quan trọng</li>
                    <li><strong>Tiến Độ:</strong> Xem tiến độ học tập trong phần Tổng Quan</li>
                </ul>
            </div>
        </div>
        <div class="modal-backdrop" data-modal="quickStartModal"></div>
    </div>

    <div class="modal hidden" id="recommendationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🎲 Gợi Ý Ngẫu Nhiên</h3>
                <button class="modal-close" data-modal="recommendationModal">×</button>
            </div>
            <div class="modal-body" id="recommendationContent">
                <!-- Content will be generated by JavaScript -->
            </div>
        </div>
        <div class="modal-backdrop" data-modal="recommendationModal"></div>
    </div>

    <script src="app.js"></script>
</body>
</html>