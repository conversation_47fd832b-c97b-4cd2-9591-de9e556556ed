<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Developer Toolkit Handbook</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header__content">
                <h1 class="header__title">🚀 Backend Developer Toolkit</h1>
                <div class="header__actions">
                    <div class="search-container">
                        <input type="text" id="searchInput" class="search-input" placeholder="Tìm kiếm kỹ năng, ví dụ...">
                        <button class="search-btn" id="searchBtn">🔍</button>
                    </div>
                    <button class="btn btn--outline btn--sm" id="themeToggle">🌙</button>
                    <button class="btn btn--primary btn--sm" id="progressBtn">📊 Tiến độ</button>
                </div>
            </div>
        </header>

        <div class="main-layout">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar__header">
                    <h2>Danh mục kỹ năng</h2>
                    <button class="sidebar__toggle" id="sidebarToggle">📋</button>
                </div>
                
                <nav class="sidebar__nav" id="sidebarNav">
                    <!-- Navigation items will be generated by JS -->
                </nav>

                <div class="sidebar__footer">
                    <div class="progress-overview" id="progressOverview">
                        <h3>Tiến độ tổng thể</h3>
                        <div class="progress-bar">
                            <div class="progress-bar__fill" id="overallProgress"></div>
                        </div>
                        <span class="progress-text" id="progressText">0% hoàn thành</span>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="content" id="mainContent">
                <div class="content__header">
                    <div class="breadcrumb" id="breadcrumb">
                        <span>Trang chủ</span>
                    </div>
                    <div class="content__actions">
                        <button class="btn btn--outline btn--sm" id="bookmarkBtn">⭐ Đánh dấu</button>
                        <button class="btn btn--outline btn--sm" id="exportBtn">📄 Xuất</button>
                    </div>
                </div>

                <div class="content__body" id="contentBody">
                    <!-- Content will be dynamically loaded -->
                    <div class="welcome-screen">
                        <h1>Chào mừng đến với Backend Developer Toolkit</h1>
                        <p>Một bộ công cụ toàn diện giúp bạn master các kỹ năng backend development cần thiết.</p>
                        
                        <div class="feature-grid">
                            <div class="feature-card">
                                <div class="feature-icon">🏗️</div>
                                <h3>Kiến trúc hệ thống</h3>
                                <p>Software archaeology, microservices, design patterns</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">🔍</div>
                                <h3>Debugging & Troubleshooting</h3>
                                <p>Systematic debugging, performance profiling, monitoring</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">🗄️</div>
                                <h3>Database & Data Management</h3>
                                <p>Zero-downtime migrations, optimization, scaling</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">⚡</div>
                                <h3>Performance Optimization</h3>
                                <p>Caching strategies, query optimization, monitoring</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">🔐</div>
                                <h3>Security & Authentication</h3>
                                <p>JWT, OAuth, security best practices</p>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">📊</div>
                                <h3>Monitoring & Observability</h3>
                                <p>Logging, metrics, distributed tracing</p>
                            </div>
                        </div>

                        <div class="quick-start">
                            <h2>Bắt đầu nhanh</h2>
                            <p>Chọn một danh mục từ sidebar để khám phá các kỹ năng và ví dụ cụ thể.</p>
                            <button class="btn btn--primary" id="getStartedBtn">Bắt đầu học →</button>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Modal for Progress Details -->
        <div class="modal hidden" id="progressModal">
            <div class="modal__backdrop" id="modalBackdrop"></div>
            <div class="modal__content">
                <div class="modal__header">
                    <h2>Chi tiết tiến độ học tập</h2>
                    <button class="modal__close" id="modalClose">✕</button>
                </div>
                <div class="modal__body" id="progressModalBody">
                    <!-- Progress details will be populated here -->
                </div>
            </div>
        </div>

        <!-- Notification Toast -->
        <div class="toast hidden" id="toast">
            <span id="toastMessage"></span>
            <button class="toast__close" id="toastClose">✕</button>
        </div>

        <!-- Search Results Overlay -->
        <div class="search-overlay hidden" id="searchOverlay">
            <div class="search-results" id="searchResults">
                <!-- Search results will be populated here -->
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>