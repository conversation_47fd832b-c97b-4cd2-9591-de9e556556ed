<!DOCTYPE html>
<html lang="vi" data-color-scheme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Software Refactoring Toolkit - Cẩ<PERSON></title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔧</text></svg>">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🔧</span>
                    <div class="logo-text">
                        <h1>Software Refactoring Toolkit</h1>
                        <span class="tagline">Cẩm <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> Thứ<PERSON>t Lõi <PERSON>ốt S<PERSON>hiệp</span>
                    </div>
                </div>
                <div class="search-container">
                    <input type="text" id="searchInput" placeholder="Tìm kiếm..." class="search-input">
                    <button class="search-btn">🔍</button>
                </div>
                <div class="theme-toggle">
                    <button id="theme-toggle" class="btn btn--secondary" title="Toggle theme">🌙</button>
                </div>
            </div>
        </header>

        <div class="main-container">
            <!-- Sidebar Navigation -->
            <nav class="sidebar" id="sidebar">
                <div class="sidebar-toggle" id="sidebarToggle">
                    <span>☰</span>
                </div>
                <div class="nav-sections">
                    <div class="nav-item active" data-section="getting_started">
                        <span class="nav-icon">🚀</span>
                        <span class="nav-text">Bắt Đầu</span>
                    </div>
                    <div class="nav-item" data-section="surgeon_mindset">
                        <span class="nav-icon">🏥</span>
                        <span class="nav-text">Tư Duy Phẫu Thuật Viên</span>
                    </div>
                    <div class="nav-item" data-section="safety_methodology">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">Phương Pháp An Toàn</span>
                    </div>
                    <div class="nav-item" data-section="code_smells">
                        <span class="nav-icon">👃</span>
                        <span class="nav-text">Phát Hiện Code Smell</span>
                    </div>
                    <div class="nav-item" data-section="refactoring_catalog">
                        <span class="nav-icon">📚</span>
                        <span class="nav-text">Thư Viện Refactoring</span>
                    </div>
                    <div class="nav-item" data-section="solid_principles">
                        <span class="nav-icon">🏗️</span>
                        <span class="nav-text">Nguyên Lý SOLID</span>
                    </div>
                    <div class="nav-item" data-section="workflow_integration">
                        <span class="nav-icon">🔄</span>
                        <span class="nav-text">Tích Hợp Workflow</span>
                    </div>
                    <div class="nav-item" data-section="tech_debt_management">
                        <span class="nav-icon">💳</span>
                        <span class="nav-text">Quản Lý Technical Debt</span>
                    </div>
                    <div class="nav-item" data-section="interactive_labs">
                        <span class="nav-icon">🧪</span>
                        <span class="nav-text">Phòng Lab Tương Tác</span>
                    </div>
                    <div class="nav-item" data-section="quick_reference">
                        <span class="nav-icon">⚡</span>
                        <span class="nav-text">Tham Khảo Nhanh</span>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="content" id="content">
                <!-- Getting Started Section -->
                <section id="getting_started" class="content-section active">
                    <div class="section-header">
                        <h2><span class="section-icon">🚀</span> Bắt Đầu</h2>
                        <div class="section-actions">
                            <button class="bookmark-btn" data-section="getting_started">🔖</button>
                            <button class="print-btn">🖨️</button>
                        </div>
                    </div>
                    
                    <div class="intro-card">
                        <h3>Software Refactoring là gì?</h3>
                        <p>Software Refactoring là nghệ thuật phẫu thuật của việc chuyển đổi mã nguồn - một kỹ thuật có kỷ luật để tái cấu trúc phần thân của mã nguồn hiện có, thay đổi cấu trúc bên trong mà không thay đổi hành vi bên ngoài. Đây là kỹ năng cốt lõi, an toàn và có tính chuyển đổi mạnh mẽ.</p>
                    </div>

                    <div class="expandable-section expanded">
                        <h3 class="expandable-header">Tại sao kỹ năng này quan trọng cho sự nghiệp? <span class="expand-icon">▼</span></h3>
                        <div class="expandable-content">
                            <ul class="feature-list">
                                <li>Mã nguồn sạch giảm chi phí bảo trì 40-60%</li>
                                <li>Hệ thống được refactor tốt thích ứng với thay đổi nhanh hơn 3 lần</li>
                                <li>Technical debt tăng 23% mỗi năm nếu không được quản lý</li>
                                <li>Thăng tiến sự nghiệp đòi hỏi khả năng cải thiện hệ thống hiện có</li>
                                <li>Phát triển hiện đại là 80% bảo trì, 20% tính năng mới</li>
                            </ul>
                        </div>
                    </div>

                    <div class="economic-value-section">
                        <h3>Giá trị kinh tế</h3>
                        <div class="value-grid">
                            <div class="value-item">
                                <div class="value-number">75%</div>
                                <div class="value-desc">Giảm thời gian sửa lỗi</div>
                            </div>
                            <div class="value-item">
                                <div class="value-number">2-3x</div>
                                <div class="value-desc">Tăng tốc phát triển tính năng</div>
                            </div>
                            <div class="value-item">
                                <div class="value-number">90%</div>
                                <div class="value-desc">Ngăn ngừa phá sản kỹ thuật</div>
                            </div>
                        </div>
                    </div>

                    <div class="quick-start-guide">
                        <h3>Checklist Bắt Đầu Nhanh</h3>
                        <div class="checklist-container">
                            <label class="checklist-item">
                                <input type="checkbox">
                                <span>✅ Đảm bảo có test coverage toàn diện</span>
                            </label>
                            <label class="checklist-item">
                                <input type="checkbox">
                                <span>✅ Xác định code smell cụ thể cần xử lý</span>
                            </label>
                            <label class="checklist-item">
                                <input type="checkbox">
                                <span>✅ Chọn kỹ thuật refactoring phù hợp</span>
                            </label>
                            <label class="checklist-item">
                                <input type="checkbox">
                                <span>✅ Lập kế hoạch các bước nhỏ, tăng dần</span>
                            </label>
                            <label class="checklist-item">
                                <input type="checkbox">
                                <span>✅ Chạy test sau mỗi thay đổi nhỏ</span>
                            </label>
                            <label class="checklist-item">
                                <input type="checkbox">
                                <span>✅ Commit thường xuyên với message mô tả</span>
                            </label>
                        </div>
                    </div>

                    <div class="call-to-action">
                        <h3>Sẵn sàng bắt đầu?</h3>
                        <p>Điều hướng đến phần <strong>Tư Duy Phẫu Thuật Viên</strong> để học cách tiếp cận an toàn và có hệ thống cho mọi tác vụ refactoring.</p>
                        <button class="btn btn--primary" data-navigate="surgeon_mindset">Bắt đầu với Tư Duy →</button>
                    </div>
                </section>

                <!-- Dynamic content will be loaded here -->
                <div id="dynamicContent"></div>
            </main>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="#quick_reference" class="footer-link">Tham Khảo Nhanh</a>
                    <a href="#interactive_labs" class="footer-link">Phòng Lab</a>
                    <a href="#tech_debt_management" class="footer-link">Technical Debt</a>
                </div>
                <div class="footer-info">
                    <p>Software Refactoring Toolkit - Cẩm Nang Suốt Sự Nghiệp Của Bạn</p>
                </div>
            </div>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>