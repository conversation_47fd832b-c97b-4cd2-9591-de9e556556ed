/* Golang Handbook Toolkit Styles */
:root {
    --bg-color: #f8f9fa;
    --text-color: #212529;
    --header-bg: #ffffff;
    --sidebar-bg: #ffffff;
    --border-color: #dee2e6;
    --primary-color: #007d9c;
    --hover-bg: #e9ecef;
}

[data-theme="dark"] {
    --bg-color: #121212;
    --text-color: #e0e0e0;
    --header-bg: #1e1e1e;
    --sidebar-bg: #1e1e1e;
    --border-color: #333;
    --primary-color: #61dafb;
    --hover-bg: #2a2a2a;
}

body {
    font-family: sans-serif;
    margin: 0;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s, color 0.3s;
}

.header {
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.header-title {
    margin: 0;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.header-controls {
    display: flex;
    align-items: center;
}

.search-container {
    display: flex;
    margin-right: 1rem;
}

.search-input {
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    padding: 0.5rem;
}

.search-btn, .theme-toggle {
    border: 1px solid var(--border-color);
    background-color: transparent;
    cursor: pointer;
    padding: 0.5rem;
}

.search-btn {
    border-left: none;
    border-radius: 0 4px 4px 0;
}

.theme-toggle {
    border-radius: 4px;
}

.main-layout {
    display: flex;
    max-width: 1200px;
    margin: 1rem auto;
}

.sidebar {
    width: 250px;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
}

.sidebar-header h3 {
    margin-top: 0;
}

.nav-item {
    padding: 0.75rem;
    cursor: pointer;
    border-radius: 4px;
}

.nav-item:hover, .nav-item.active {
    background-color: var(--hover-bg);
    color: var(--primary-color);
}

.main-content {
    flex-grow: 1;
    padding: 1rem;
}

.content-section {
    background-color: var(--header-bg);
    padding: 2rem;
    border-radius: 8px;
}

pre {
    background-color: var(--hover-bg);
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
}

code {
    font-family: 'Courier New', Courier, monospace;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    text-align: left;
}

th {
    background-color: var(--hover-bg);
}

