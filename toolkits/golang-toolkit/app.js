document.addEventListener('DOMContentLoaded', () => {
    const themeToggle = document.getElementById('themeToggle');
    const searchInput = document.getElementById('searchInput');
    const contentSection = document.getElementById('section-content');
    const navItems = document.querySelectorAll('.nav-item');

    function setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        themeToggle.textContent = theme === 'dark' ? '☀️' : '🌙';
    }

    // Theme switcher
    themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        setTheme(newTheme);
        // Notify parent of the change
        if (window.parent) {
            window.parent.postMessage({ type: 'THEME_CHANGE', theme: newTheme }, '*');
        }
    });

    // Listen for theme changes from parent
    window.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'THEME_CHANGE') {
            setTheme(event.data.theme);
        }
    });

    // Navigation
    navItems.forEach(item => {
        item.addEventListener('click', () => {
            const topic = item.getAttribute('data-topic');
            loadContent(topic);
            navItems.forEach(i => i.classList.remove('active'));
            item.classList.add('active');
        });
    });

    // Content loading
    async function loadContent(topic) {
        let url = '';
        let content = '';
        switch (topic) {
            case 'handbook':
                url = '../../golang-docs/handbook.md';
                content = await fetchAndParse(url, 'md');
                break;
            case 'data_types':
                url = '../../golang-docs/data_type.csv';
                content = await fetchAndParse(url, 'csv');
                break;
            case 'best_practices':
                url = '../../golang-docs/best_practice.csv';
                content = await fetchAndParse(url, 'csv');
                break;
        }
        contentSection.innerHTML = content;
    }

    async function fetchAndParse(url, format) {
        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const text = await response.text();
            if (format === 'md') return parseMarkdown(text);
            if (format === 'csv') return parseCsv(text);
        } catch (e) {
            return `<p>Error loading content: ${e.message}</p>`;
        }
    }

    function parseMarkdown(text) {
        // Simple markdown to HTML conversion
        text = text.replace(/^# (.*$)/gim, '<h1>$1</h1>');
        text = text.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        text = text.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        text = text.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
        text = text.replace(/\*(.*)\*/gim, '<em>$1</em>');
        text = text.replace(/```go([^`]+)```/gim, '<pre><code>$1</code></pre>');
        text = text.replace(/`([^`]+)`/gim, '<code>$1</code>');
        return text.split('\n').map(p => `<p>${p}</p>`).join('');
    }

    function parseCsv(text) {
        const rows = text.split('\n').filter(row => row.trim() !== '');
        const headers = rows[0].split(',');
        const data = rows.slice(1).map(row => row.split(','));

        let table = '<table><thead><tr>';
        headers.forEach(header => table += `<th>${header}</th>`);
        table += '</tr></thead><tbody>';
        data.forEach(rowData => {
            table += '<tr>';
            rowData.forEach(cell => table += `<td>${cell}</td>`);
            table += '</tr>';
        });
        table += '</tbody></table>';
        return table;
    }

    // Initial load
    loadContent('handbook');
    document.querySelector('.nav-item[data-topic="handbook"]').classList.add('active');
});
