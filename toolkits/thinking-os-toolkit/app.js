// Application Data
const appData = {
  "meta_principles": {
    "title": "Nguyên lý Meta - Bất biến qua mọi thời đại",
    "principles": [
      {
        "name": "Nguyên lý <PERSON>ủ thể (Subject Principle)",
        "description": "<PERSON><PERSON><PERSON> thực thể đều có <PERSON>hu<PERSON> t<PERSON> (Tĩnh) và Hành vi (Động)",
        "components": ["Trạng thái/State", "Hành vi/Behavior"]
      },
      {
        "name": "Nguyên lý Mâu thuẫn (Contradiction Principle)", 
        "description": "Sự phát triển sinh ra từ việc giải quyết mâu thuẫn nội tại",
        "components": ["Nhận diện mâu thuẫn", "Tìm giải pháp biện chứng", "Thống nhất đối lập"]
      },
      {
        "name": "Nguyên lý Lượ<PERSON>-Chất (Quantity-Quality Principle)",
        "description": "Sự tích lũy về lượng dẫn đến chuyển hóa về chất",
        "components": ["Theo dõi tích lũy", "Nhận diện điểm nút", "Chuẩn bị chuyển đổi"]
      },
      {
        "name": "Nguyên lý Phủ định của Phủ định (Negation Principle)",
        "description": "Phát triển theo xoáy ốc, mỗi giải pháp tạo ra vấn đề mới ở cấp độ cao hơn",
        "components": ["Hiểu lịch sử", "Dự đoán phủ định", "Chuẩn bị vòng tiếp theo"]
      },
      {
        "name": "Nguyên lý Hệ thống (Systems Principle)",
        "description": "Tổng thể lớn hơn tổng các phần, tập trung vào mối quan hệ",
        "components": ["Ranh giới", "Mối quan hệ", "Góc nhìn", "Phần-Tổng thể"]
      },
      {
        "name": "Nguyên lý Đệ quy Meta (Meta-Recursion Principle)",
        "description": "Framework tư duy phải áp dụng cho chính nó",
        "components": ["Tự phản tỉnh", "Tự cải tiến", "Tự nhận thức"]
      }
    ]
  },
  "cognitive_architecture": {
    "title": "Kiến trúc Nhận thức",
    "modules": [
      {
        "name": "Perception Module",
        "function": "Thu thập và lọc thông tin",
        "components": ["Cảm nhận", "Chú ý", "Nhận dạng mẫu"]
      },
      {
        "name": "Memory Module", 
        "function": "Lưu trữ và truy xuất kiến thức",
        "components": ["Bộ nhớ ngắn hạn", "Bộ nhớ dài hạn", "Bộ nhớ làm việc"]
      },
      {
        "name": "Reasoning Module",
        "function": "Xử lý logic và suy luận",
        "components": ["Suy luận qui nạp", "Suy luận diễn dịch", "Suy luận tương tự"]
      },
      {
        "name": "Decision Module",
        "function": "Ra quyết định và lựa chọn hành động",
        "components": ["Đánh giá lựa chọn", "Ma trận quyết định", "Quản lý rủi ro"]
      },
      {
        "name": "Metacognition Module",
        "function": "Giám sát và điều chỉnh tư duy",
        "components": ["Tự nhận thức", "Tự điều chỉnh", "Đánh giá hiệu quả"]
      }
    ]
  },
  "mental_models": {
    "universal_models": [
      {
        "name": "First Principles Thinking",
        "description": "Phân rã vấn đề về các nguyên lý cơ bản nhất",
        "steps": ["Xác định giả định", "Phân rã về cơ bản", "Xây dựng từ đầu", "Kiểm thử"],
        "applications": ["Đổi mới", "Giải quyết vấn đề phức tạp", "Tạo breakthrough"]
      },
      {
        "name": "Systems Thinking", 
        "description": "Hiểu hệ thống qua mối quan hệ và tương tác",
        "steps": ["Xác định ranh giới", "Ánh xạ mối quan hệ", "Tìm leverage points", "Hiểu feedback loops"],
        "applications": ["Quản lý phức tạp", "Thiết kế tổ chức", "Hiểu tác động"]
      },
      {
        "name": "Dialectical Thinking",
        "description": "Thống nhất các mặt đối lập",
        "steps": ["Nhận diện đối lập", "Hiểu tension", "Tìm synthesis", "Tạo unity"],
        "applications": ["Quản lý mâu thuẫn", "Đổi mới", "Leadership"]
      },
      {
        "name": "Metacognitive Monitoring",
        "description": "Tư duy về tư duy",
        "steps": ["Quan sát tư duy", "Đánh giá hiệu quả", "Điều chỉnh strategy", "Học từ sai lầm"],
        "applications": ["Học tập", "Cải tiến", "Tự nhận thức"]
      }
    ]
  }
};

// Assessment Questions
const assessmentQuestions = [
  {
    category: "Meta Principles",
    questions: [
      "Tôi có thể nhận diện được mâu thuẫn cốt lõi trong các vấn đề phức tạp",
      "Tôi hiểu được mối quan hệ giữa lượng và chất trong sự phát triển",
      "Tôi có thể áp dụng tư duy hệ thống để giải quyết vấn đề"
    ]
  },
  {
    category: "Cognitive Modules",
    questions: [
      "Tôi có khả năng lọc thông tin hiệu quả",
      "Tôi quản lý bộ nhớ và kiến thức một cách có hệ thống",
      "Tôi sử dụng nhiều loại suy luận khác nhau",
      "Tôi ra quyết định dựa trên phân tích khách quan"
    ]
  },
  {
    category: "Universal Process",
    questions: [
      "Tôi tuân thủ quy trình có hệ thống khi giải quyết vấn đề",
      "Tôi phân tích vấn đề từ nguyên lý cơ bản",
      "Tôi đánh giá các lựa chọn một cách toàn diện"
    ]
  }
];

// Application State
let currentSection = 'overview';
let decisionOptions = [];
let decisionCriteria = [];

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    initTheme();
    setupNavigation();
    setupInteractiveElements();
    setupDecisionTool();
    setupPracticeTabs();
    setupAssessment();
    setupModal();
    populateMentalModels();
    
    // Add default decision tool examples after a short delay
    setTimeout(() => {
        initializeDecisionToolDefaults();
    }, 200);
}

// Theme functionality
function initTheme() {
    const savedTheme = localStorage.getItem('unifiedToolkit.theme') || 'light';
    setTheme(savedTheme);
    
    // Listen for theme changes from parent (unified toolkit)
    window.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'THEME_CHANGE') {
            setTheme(event.data.theme);
        }
    });
    
    // Setup theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
    console.log('Theme system initialized');
}

function setTheme(theme) {
    document.documentElement.setAttribute('data-color-scheme', theme);
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.textContent = theme === 'dark' ? '☀️' : '🌙';
    }
    localStorage.setItem('unifiedToolkit.theme', theme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-color-scheme') || 'light';
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    
    // Broadcast theme change to parent (unified toolkit)
    if (window.parent !== window) {
        try {
            window.parent.postMessage({
                type: 'THEME_CHANGE',
                theme: newTheme
            }, '*');
        } catch (e) {
            // Ignore cross-origin errors
        }
    }
}

// Navigation - Fixed version
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.content-section');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetSection = link.getAttribute('data-section');
            
            console.log('Clicking nav link for section:', targetSection); // Debug log
            
            if (targetSection) {
                // Update active nav link
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
                
                // Hide all sections first
                sections.forEach(s => s.classList.remove('active'));
                
                // Show target section
                const targetElement = document.getElementById(targetSection);
                if (targetElement) {
                    targetElement.classList.add('active');
                    currentSection = targetSection;
                    console.log('Successfully switched to section:', targetSection); // Debug log
                } else {
                    console.error('Section not found:', targetSection); // Debug log
                }
            }
        });
    });
}

// Interactive Elements - Enhanced version
function setupInteractiveElements() {
    // Setup principle cards after navigation is ready
    setTimeout(() => {
        const principleCards = document.querySelectorAll('.principle-card');
        principleCards.forEach((card, index) => {
            card.addEventListener('click', () => {
                console.log('Principle card clicked:', index); // Debug log
                showPrincipleDetails(index);
            });
            
            // Make cards focusable for accessibility
            card.setAttribute('tabindex', '0');
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    showPrincipleDetails(index);
                }
            });
        });

        // Setup cognitive modules
        const cognitiveModules = document.querySelectorAll('.cognitive-module');
        cognitiveModules.forEach((module, index) => {
            module.addEventListener('click', () => {
                console.log('Cognitive module clicked:', index); // Debug log
                showModuleDetails(index);
            });
            
            // Make modules focusable for accessibility
            module.setAttribute('tabindex', '0');
            module.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    showModuleDetails(index);
                }
            });
        });

        // Setup process steps
        const stepCards = document.querySelectorAll('.step-card');
        stepCards.forEach((card, index) => {
            card.addEventListener('click', () => {
                console.log('Step card clicked:', index + 1); // Debug log
                showStepDetails(index + 1);
                updateProgressBar(index + 1);
            });
            
            // Make steps focusable for accessibility
            card.setAttribute('tabindex', '0');
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    showStepDetails(index + 1);
                    updateProgressBar(index + 1);
                }
            });
        });
    }, 300);
}

function showPrincipleDetails(index) {
    const principle = appData.meta_principles.principles[index];
    const modal = document.getElementById('info-modal');
    const title = document.getElementById('modal-title');
    const body = document.getElementById('modal-body');

    if (!principle || !modal || !title || !body) {
        console.error('Missing elements for principle modal'); // Debug log
        return;
    }

    title.textContent = principle.name;
    body.innerHTML = `
        <p><strong>Mô tả:</strong> ${principle.description}</p>
        <h4>Thành phần:</h4>
        <ul>
            ${principle.components.map(comp => `<li>${comp}</li>`).join('')}
        </ul>
        <div class="mt-24">
            <h4>Cách áp dụng:</h4>
            <p>Sử dụng nguyên lý này để phân tích và hiểu bản chất của các vấn đề trong cuộc sống và công việc.</p>
        </div>
    `;
    
    modal.classList.remove('hidden');
    console.log('Principle modal opened for:', principle.name); // Debug log
}

function showModuleDetails(index) {
    const module = appData.cognitive_architecture.modules[index];
    const modal = document.getElementById('info-modal');
    const title = document.getElementById('modal-title');
    const body = document.getElementById('modal-body');

    if (!module || !modal || !title || !body) {
        console.error('Missing elements for module modal'); // Debug log
        return;
    }

    title.textContent = module.name;
    body.innerHTML = `
        <p><strong>Chức năng:</strong> ${module.function}</p>
        <h4>Thành phần:</h4>
        <ul>
            ${module.components.map(comp => `<li>${comp}</li>`).join('')}
        </ul>
        <div class="mt-24">
            <h4>Cách phát triển:</h4>
            <p>Thực hành các bài tập cụ thể để tăng cường khả năng của module này trong hệ thống nhận thức.</p>
        </div>
    `;
    
    modal.classList.remove('hidden');
    console.log('Module modal opened for:', module.name); // Debug log
}

function showStepDetails(stepNumber) {
    const processData = {
        1: { name: "Define", action: "Nhận diện mâu thuẫn cốt lõi bằng 5W1H", details: "Sử dụng 5W1H (What, Who, When, Where, Why, How) để định nghĩa rõ ràng vấn đề cần giải quyết." },
        2: { name: "Measure", action: "Lượng hóa mâu thuẫn bằng số liệu", details: "Thu thập dữ liệu định lượng để hiểu rõ quy mô và tác động của vấn đề." },
        3: { name: "Analyze", action: "Phân tích bản chất mâu thuẫn", details: "Đi sâu vào nguyên nhân gốc rễ của mâu thuẫn." },
        4: { name: "First Principles", action: "Phân rã về nguyên lý cơ bản", details: "Phá bỏ các giả định và quay về những nguyên lý cơ bản nhất." },
        5: { name: "Systems View", action: "Xem xét trong bối cảnh hệ thống", details: "Hiểu vấn đề trong mối quan hệ với toàn bộ hệ thống." },
        6: { name: "Hypothesize", action: "Đưa ra các phương án giải quyết", details: "Brainstorm và đề xuất nhiều giải pháp khả thi." },
        7: { name: "Evaluate", action: "Đánh giá các phương án bằng ma trận", details: "Sử dụng ma trận quyết định để so sánh các lựa chọn." },
        8: { name: "Decide", action: "Chốt quyết định và ghi lại ADR", details: "Đưa ra quyết định cuối cùng và ghi lại lý do." },
        9: { name: "Design", action: "Thiết kế giải pháp chi tiết", details: "Lập kế hoạch triển khai chi tiết." },
        10: { name: "Implement", action: "Triển khai theo kế hoạch", details: "Thực hiện giải pháp theo kế hoạch đã thiết kế." },
        11: { name: "Verify", action: "Kiểm thử và xác nhận", details: "Kiểm tra kết quả và xác nhận hiệu quả." },
        12: { name: "Improve", action: "Cải tiến và chuẩn bị vòng tiếp theo", details: "Học hỏi từ kết quả và chuẩn bị cho chu kỳ tiếp theo." }
    };

    const step = processData[stepNumber];
    const modal = document.getElementById('info-modal');
    const title = document.getElementById('modal-title');
    const body = document.getElementById('modal-body');

    if (!step || !modal || !title || !body) {
        console.error('Missing elements for step modal'); // Debug log
        return;
    }

    title.textContent = `Bước ${stepNumber}: ${step.name}`;
    body.innerHTML = `
        <p><strong>Hành động:</strong> ${step.action}</p>
        <p><strong>Chi tiết:</strong> ${step.details}</p>
        <div class="mt-24">
            <h4>Công cụ hỗ trợ:</h4>
            <ul>
                <li>Template checklist</li>
                <li>Worksheet thực hành</li>
                <li>Ví dụ minh họa</li>
            </ul>
        </div>
    `;
    
    modal.classList.remove('hidden');
    console.log('Step modal opened for step:', stepNumber); // Debug log
}

function updateProgressBar(currentStep) {
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        const percentage = (currentStep / 12) * 100;
        progressFill.style.width = `${percentage}%`;
        console.log('Progress updated to:', percentage + '%'); // Debug log
    }
}

// Mental Models - Enhanced version
function populateMentalModels() {
    const modelsGrid = document.getElementById('models-grid');
    const searchInput = document.getElementById('models-search');

    if (!modelsGrid || !searchInput) {
        console.error('Mental models elements not found'); // Debug log
        return;
    }

    function renderModels(models) {
        modelsGrid.innerHTML = '';
        models.forEach(model => {
            const modelCard = document.createElement('div');
            modelCard.className = 'model-card';
            modelCard.innerHTML = `
                <h3>${model.name}</h3>
                <p>${model.description}</p>
                <h4>Các bước:</h4>
                <ul class="model-steps">
                    ${model.steps.map(step => `<li>${step}</li>`).join('')}
                </ul>
                <div class="model-applications">
                    ${model.applications.map(app => `<span class="application-tag">${app}</span>`).join('')}
                </div>
            `;
            modelsGrid.appendChild(modelCard);
        });
        console.log('Rendered', models.length, 'mental models'); // Debug log
    }

    // Initial render
    renderModels(appData.mental_models.universal_models);

    // Search functionality
    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        const filteredModels = appData.mental_models.universal_models.filter(model => 
            model.name.toLowerCase().includes(query) ||
            model.description.toLowerCase().includes(query) ||
            model.applications.some(app => app.toLowerCase().includes(query))
        );
        renderModels(filteredModels);
        console.log('Search results:', filteredModels.length, 'models for query:', query); // Debug log
    });
}

// Decision Tool - Enhanced version
function setupDecisionTool() {
    const addOptionBtn = document.getElementById('add-option');
    const addCriteriaBtn = document.getElementById('add-criteria');
    const calculateBtn = document.getElementById('calculate-decision');

    if (!addOptionBtn || !addCriteriaBtn || !calculateBtn) {
        console.error('Decision tool elements not found'); // Debug log
        return;
    }

    addOptionBtn.addEventListener('click', addOption);
    addCriteriaBtn.addEventListener('click', addCriteria);
    calculateBtn.addEventListener('click', calculateDecision);
    
    console.log('Decision tool setup complete'); // Debug log
}

function addOption() {
    const optionsList = document.getElementById('options-list');
    if (!optionsList) return;
    
    const optionIndex = decisionOptions.length;
    
    const optionDiv = document.createElement('div');
    optionDiv.className = 'option-item';
    optionDiv.innerHTML = `
        <input type="text" class="form-control" placeholder="Lựa chọn ${optionIndex + 1}" data-option-index="${optionIndex}">
        <button class="remove-btn" onclick="removeOption(${optionIndex})">Xóa</button>
    `;
    
    optionsList.appendChild(optionDiv);
    decisionOptions.push({ name: '', scores: {} });
    console.log('Added option', optionIndex + 1); // Debug log
}

function addCriteria() {
    const criteriaList = document.getElementById('criteria-list');
    if (!criteriaList) return;
    
    const criteriaIndex = decisionCriteria.length;
    
    const criteriaDiv = document.createElement('div');
    criteriaDiv.className = 'criteria-item';
    criteriaDiv.innerHTML = `
        <input type="text" class="form-control" placeholder="Tiêu chí ${criteriaIndex + 1}" data-criteria-index="${criteriaIndex}">
        <input type="number" class="form-control weight-input" placeholder="Trọng số" min="1" max="10" value="5" data-weight-index="${criteriaIndex}">
        <button class="remove-btn" onclick="removeCriteria(${criteriaIndex})">Xóa</button>
    `;
    
    criteriaList.appendChild(criteriaDiv);
    decisionCriteria.push({ name: '', weight: 5 });
    console.log('Added criteria', criteriaIndex + 1); // Debug log
}

// Global functions for remove buttons
window.removeOption = function(index) {
    const optionsList = document.getElementById('options-list');
    if (optionsList && optionsList.children[index]) {
        optionsList.children[index].remove();
        decisionOptions.splice(index, 1);
        console.log('Removed option', index); // Debug log
    }
};

window.removeCriteria = function(index) {
    const criteriaList = document.getElementById('criteria-list');
    if (criteriaList && criteriaList.children[index]) {
        criteriaList.children[index].remove();
        decisionCriteria.splice(index, 1);
        console.log('Removed criteria', index); // Debug log
    }
};

function calculateDecision() {
    // Update options and criteria from inputs
    const optionInputs = document.querySelectorAll('[data-option-index]');
    const criteriaInputs = document.querySelectorAll('[data-criteria-index]');
    const weightInputs = document.querySelectorAll('[data-weight-index]');

    optionInputs.forEach((input, index) => {
        if (decisionOptions[index]) {
            decisionOptions[index].name = input.value;
        }
    });

    criteriaInputs.forEach((input, index) => {
        if (decisionCriteria[index]) {
            decisionCriteria[index].name = input.value;
        }
    });

    weightInputs.forEach((input, index) => {
        if (decisionCriteria[index]) {
            decisionCriteria[index].weight = parseInt(input.value) || 5;
        }
    });

    if (decisionOptions.length === 0 || decisionCriteria.length === 0) {
        alert('Vui lòng thêm ít nhất một lựa chọn và một tiêu chí.');
        return;
    }

    // Create scoring matrix
    const resultsDiv = document.getElementById('matrix-results');
    if (!resultsDiv) return;
    
    let tableHTML = `
        <h4>Ma trận đánh giá</h4>
        <table class="results-table">
            <thead>
                <tr>
                    <th>Lựa chọn</th>
                    ${decisionCriteria.map(criteria => `<th>${criteria.name} (${criteria.weight})</th>`).join('')}
                    <th>Tổng điểm</th>
                </tr>
            </thead>
            <tbody>
    `;

    const scores = [];
    decisionOptions.forEach((option, optionIndex) => {
        let totalScore = 0;
        let rowHTML = `<tr><td>${option.name}</td>`;
        
        decisionCriteria.forEach((criteria, criteriaIndex) => {
            // Generate random score for demo (in real app, user would input these)
            const score = Math.floor(Math.random() * 10) + 1;
            const weightedScore = score * criteria.weight;
            totalScore += weightedScore;
            rowHTML += `<td>${score} (${weightedScore})</td>`;
        });
        
        rowHTML += `<td><strong>${totalScore}</strong></td></tr>`;
        tableHTML += rowHTML;
        scores.push({ option: option.name, score: totalScore });
    });

    // Find winner
    const winner = scores.reduce((prev, current) => (prev.score > current.score) ? prev : current);
    
    tableHTML += `</tbody></table>`;
    tableHTML += `<div class="mt-24"><h4>Kết quả: <span style="color: var(--color-success)">${winner.option}</span> với ${winner.score} điểm</h4></div>`;

    resultsDiv.innerHTML = tableHTML;
    console.log('Decision calculation complete'); // Debug log
}

// Practice Tabs - Enhanced version
function setupPracticeTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    if (tabBtns.length === 0 || tabContents.length === 0) {
        console.error('Practice tab elements not found'); // Debug log
        return;
    }

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');
            
            if (!targetTab) return;
            
            // Update active tab button
            tabBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Show target tab content
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.getAttribute('data-tab') === targetTab) {
                    content.classList.add('active');
                }
            });
            
            console.log('Switched to practice tab:', targetTab); // Debug log
        });
    });
}

// Assessment - Enhanced version
function setupAssessment() {
    const questionsContainer = document.getElementById('assessment-questions');
    const calculateBtn = document.getElementById('calculate-assessment');

    if (!questionsContainer || !calculateBtn) {
        console.error('Assessment elements not found'); // Debug log
        return;
    }

    // Populate questions
    let questionHTML = '';
    let questionIndex = 0;
    
    assessmentQuestions.forEach(category => {
        questionHTML += `<div class="category-section">
            <h4 style="color: var(--color-primary); margin: var(--space-24) 0 var(--space-16) 0;">${category.category}</h4>`;
        
        category.questions.forEach(question => {
            questionHTML += `
                <div class="question-item">
                    <h4>${question}</h4>
                    <div class="rating-scale">
                        ${[1,2,3,4,5].map(value => `
                            <label>
                                <input type="radio" name="question_${questionIndex}" value="${value}">
                                ${value}
                            </label>
                        `).join('')}
                    </div>
                    <div class="scale-labels">
                        <span>Hoàn toàn không đồng ý</span>
                        <span>Hoàn toàn đồng ý</span>
                    </div>
                </div>
            `;
            questionIndex++;
        });
        
        questionHTML += '</div>';
    });

    questionsContainer.innerHTML = questionHTML;
    calculateBtn.addEventListener('click', calculateAssessment);
    
    console.log('Assessment setup complete with', questionIndex, 'questions'); // Debug log
}

function calculateAssessment() {
    const totalQuestions = assessmentQuestions.reduce((sum, category) => sum + category.questions.length, 0);
    const responses = [];
    
    for (let i = 0; i < totalQuestions; i++) {
        const selectedValue = document.querySelector(`input[name="question_${i}"]:checked`);
        if (selectedValue) {
            responses.push(parseInt(selectedValue.value));
        }
    }

    if (responses.length < totalQuestions) {
        alert('Vui lòng trả lời tất cả các câu hỏi.');
        return;
    }

    // Calculate scores by category
    const categoryScores = [];
    let responseIndex = 0;
    
    assessmentQuestions.forEach(category => {
        const categoryResponses = responses.slice(responseIndex, responseIndex + category.questions.length);
        const averageScore = categoryResponses.reduce((sum, score) => sum + score, 0) / categoryResponses.length;
        categoryScores.push({
            category: category.category,
            score: averageScore,
            percentage: (averageScore / 5) * 100
        });
        responseIndex += category.questions.length;
    });

    // Show results
    const resultsDiv = document.getElementById('assessment-results');
    if (resultsDiv) {
        resultsDiv.classList.remove('hidden');
        
        // Create chart
        createAssessmentChart(categoryScores);
        
        // Generate recommendations
        generateRecommendations(categoryScores);
        
        console.log('Assessment complete:', categoryScores); // Debug log
    }
}

function createAssessmentChart(categoryScores) {
    const ctx = document.getElementById('assessment-chart');
    if (!ctx) return;
    
    const chartContext = ctx.getContext('2d');
    
    new Chart(chartContext, {
        type: 'radar',
        data: {
            labels: categoryScores.map(item => item.category),
            datasets: [{
                label: 'Điểm số hiện tại',
                data: categoryScores.map(item => item.percentage),
                backgroundColor: 'rgba(31, 184, 205, 0.2)',
                borderColor: 'rgba(31, 184, 205, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(31, 184, 205, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(31, 184, 205, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                title: {
                    display: true,
                    text: 'Kết quả đánh giá TOS Framework'
                }
            }
        }
    });
    
    console.log('Assessment chart created'); // Debug log
}

function generateRecommendations(categoryScores) {
    const recommendationsDiv = document.getElementById('recommendations');
    if (!recommendationsDiv) return;
    
    let recommendationsHTML = '<h4>Khuyến nghị cải thiện:</h4>';

    categoryScores.forEach(category => {
        if (category.percentage < 60) {
            recommendationsHTML += `
                <div class="recommendation-item">
                    <h4>${category.category} - Cần cải thiện</h4>
                    <p>Điểm số của bạn trong lĩnh vực này là ${category.score.toFixed(1)}/5. 
                    Hãy tập trung thực hành nhiều hơn và học các kỹ thuật liên quan.</p>
                </div>
            `;
        } else if (category.percentage < 80) {
            recommendationsHTML += `
                <div class="recommendation-item">
                    <h4>${category.category} - Tốt</h4>
                    <p>Điểm số của bạn trong lĩnh vực này là ${category.score.toFixed(1)}/5. 
                    Tiếp tục duy trì và phát triển thêm.</p>
                </div>
            `;
        } else {
            recommendationsHTML += `
                <div class="recommendation-item" style="border-left-color: var(--color-success)">
                    <h4>${category.category} - Xuất sắc</h4>
                    <p>Điểm số của bạn trong lĩnh vực này là ${category.score.toFixed(1)}/5. 
                    Tuyệt vời! Hãy chia sẻ kinh nghiệm với người khác.</p>
                </div>
            `;
        }
    });

    recommendationsDiv.innerHTML = recommendationsHTML;
    console.log('Recommendations generated'); // Debug log
}

// Modal - Enhanced version
function setupModal() {
    const modal = document.getElementById('info-modal');
    const closeBtn = document.getElementById('close-modal');

    if (!modal || !closeBtn) {
        console.error('Modal elements not found'); // Debug log
        return;
    }

    closeBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.classList.add('hidden');
        }
    });

    // Close modal on Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
        }
    });
    
    console.log('Modal setup complete'); // Debug log
}

// Initialize default decision tool data
function initializeDecisionToolDefaults() {
    // Add default options and criteria for demo
    addOption();
    addOption();
    addCriteria();
    addCriteria();
    
    // Fill in some example data
    setTimeout(() => {
        const optionInputs = document.querySelectorAll('[data-option-index]');
        const criteriaInputs = document.querySelectorAll('[data-criteria-index]');
        
        if (optionInputs.length >= 2) {
            optionInputs[0].value = "Lựa chọn A";
            optionInputs[1].value = "Lựa chọn B";
        }
        
        if (criteriaInputs.length >= 2) {
            criteriaInputs[0].value = "Chi phí";
            criteriaInputs[1].value = "Hiệu quả";
        }
    }, 100);
    
    console.log('Decision tool defaults initialized'); // Debug log
}

// Export Functions (for future use)
function exportDecisionRecord() {
    const context = document.getElementById('decision-context');
    if (!context) return;
    
    const record = {
        context: context.value,
        options: decisionOptions,
        criteria: decisionCriteria,
        timestamp: new Date().toISOString()
    };
    
    const dataStr = JSON.stringify(record, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `decision-record-${Date.now()}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
}

// Utility Functions
function showSuccessMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'success-message';
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

function showErrorMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'error-message';
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}