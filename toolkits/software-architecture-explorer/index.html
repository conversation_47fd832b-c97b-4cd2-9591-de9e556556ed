<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kiến <PERSON>c Software Architecture & Enterprise Application Design</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1>Kiến Thức Software Architecture</h1>
            <div class="header-actions">
                <div class="search-container">
                    <input type="text" id="search-input" class="search-input" placeholder="Tìm kiếm patterns, concepts...">
                    <button id="search-btn" class="btn btn--primary btn--sm">Tìm kiếm</button>
                </div>
                <button id="theme-toggle" class="btn btn--outline btn--sm" title="Toggle theme">🌙</button>
            </div>
        </div>
    </header>

    <div class="app-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <ul class="nav-list">
                <li class="nav-item">
                    <button class="nav-button active" data-section="overview">
                        <span class="nav-icon">🏗️</span>
                        Tổng quan
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="roles">
                        <span class="nav-icon">👥</span>
                        Vai trò Architect
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="design-levels">
                        <span class="nav-icon">📊</span>
                        Mức độ Thiết kế
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="architecture-patterns">
                        <span class="nav-icon">🏛️</span>
                        Architecture Patterns
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="design-patterns">
                        <span class="nav-icon">🎨</span>
                        Design Patterns
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="enterprise-patterns">
                        <span class="nav-icon">🏢</span>
                        Enterprise Patterns
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="integration-patterns">
                        <span class="nav-icon">🔗</span>
                        Integration Patterns
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="implementation">
                        <span class="nav-icon">⚙️</span>
                        Hướng dẫn Triển khai
                    </button>
                </li>
                <li class="nav-item">
                    <button class="nav-button" data-section="decision-framework">
                        <span class="nav-icon">🎯</span>
                        Framework Quyết định
                    </button>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <div class="section-header">
                    <h2>Tổng quan Kiến thức Software Architecture</h2>
                    <p>Hệ thống kiến thức toàn diện về thiết kế kiến trúc phần mềm từ chiến lược đến triển khai</p>
                </div>

                <div class="overview-grid">
                    <div class="overview-card">
                        <div class="card-icon">🏗️</div>
                        <h3>Kiến trúc Hệ thống</h3>
                        <p>Thiết kế cấu trúc tổng thể và quyết định công nghệ quan trọng</p>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">🎯</div>
                        <h3>Patterns & Practices</h3>
                        <p>Tập hợp các mẫu thiết kế đã được kiểm chứng và thực hành tốt nhất</p>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">⚡</div>
                        <h3>Framework Lifecycle</h3>
                        <p>Hiểu rõ chu trình hoạt động và điểm mở rộng của framework</p>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">🔄</div>
                        <h3>Integration Strategies</h3>
                        <p>Chiến lược tích hợp hệ thống và quản lý độ phức tạp</p>
                    </div>
                </div>

                <div class="hierarchy-image">
                    <img src="https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/57c48d439216856a7ce7b756bb24addc/8523c278-c872-4e8e-94de-1a9009459f4c/ba36ba63.png" 
                         alt="Sơ đồ phân cấp kiến thức Software Architecture" 
                         class="hierarchy-diagram">
                    <p class="image-caption">Sơ đồ phân cấp kiến thức từ Strategic Design đến Implementation</p>
                </div>
            </section>

            <!-- Roles Section -->
            <section id="roles" class="content-section">
                <div class="section-header">
                    <h2>Vai trò trong Kiến trúc Phần mềm</h2>
                    <p>So sánh và phân tích các vai trò chính trong thiết kế kiến trúc</p>
                </div>

                <div class="roles-comparison">
                    <div class="role-card">
                        <h3>Software Architect</h3>
                        <div class="role-content">
                            <h4>Trách nhiệm chính:</h4>
                            <ul class="role-list" id="architect-responsibilities"></ul>
                            <h4>Kỹ năng cần thiết:</h4>
                            <ul class="role-list" id="architect-skills"></ul>
                            <h4>Cấp độ:</h4>
                            <div class="levels" id="architect-levels"></div>
                        </div>
                    </div>

                    <div class="role-card">
                        <h3>Enterprise Application Designer</h3>
                        <div class="role-content">
                            <h4>Trách nhiệm chính:</h4>
                            <ul class="role-list" id="designer-responsibilities"></ul>
                            <h4>Kỹ năng cần thiết:</h4>
                            <ul class="role-list" id="designer-skills"></ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Design Levels Section -->
            <section id="design-levels" class="content-section">
                <div class="section-header">
                    <h2>Mức độ Thiết kế</h2>
                    <p>Phân biệt giữa thiết kế chiến lược và chiến thuật</p>
                </div>

                <div class="design-levels-container">
                    <div class="level-section">
                        <h3>Strategic Design - Thiết kế Chiến lược</h3>
                        <p class="level-description">Tập trung vào hiểu biết cấp cao về business domain</p>
                        <div class="techniques-grid" id="strategic-techniques"></div>
                    </div>

                    <div class="level-section">
                        <h3>Tactical Design - Thiết kế Chiến thuật</h3>
                        <p class="level-description">Patterns triển khai cụ thể trong bounded contexts</p>
                        <div class="techniques-grid" id="tactical-techniques"></div>
                    </div>
                </div>
            </section>

            <!-- Architecture Patterns Section -->
            <section id="architecture-patterns" class="content-section">
                <div class="section-header">
                    <h2>Architecture Patterns</h2>
                    <p>Các mẫu kiến trúc cấu trúc và tích hợp hệ thống</p>
                </div>

                <div class="patterns-tabs">
                    <button class="tab-button active" data-tab="structural">Structural Patterns</button>
                    <button class="tab-button" data-tab="integration">Integration Patterns</button>
                </div>

                <div class="tab-content">
                    <div id="structural-patterns" class="tab-pane active">
                        <div class="patterns-grid"></div>
                    </div>
                    <div id="integration-patterns-tab" class="tab-pane">
                        <div class="patterns-grid"></div>
                    </div>
                </div>
            </section>

            <!-- Design Patterns Section -->
            <section id="design-patterns" class="content-section">
                <div class="section-header">
                    <h2>Design Patterns</h2>
                    <p>Các mẫu thiết kế cổ điển: Creational, Structural, Behavioral</p>
                </div>

                <div class="patterns-tabs">
                    <button class="tab-button active" data-tab="creational">Creational</button>
                    <button class="tab-button" data-tab="structural-design">Structural</button>
                    <button class="tab-button" data-tab="behavioral">Behavioral</button>
                </div>

                <div class="tab-content">
                    <div id="creational-patterns" class="tab-pane active">
                        <div class="patterns-grid"></div>
                    </div>
                    <div id="structural-design-patterns" class="tab-pane">
                        <div class="patterns-grid"></div>
                    </div>
                    <div id="behavioral-patterns" class="tab-pane">
                        <div class="patterns-grid"></div>
                    </div>
                </div>
            </section>

            <!-- Enterprise Patterns Section -->
            <section id="enterprise-patterns" class="content-section">
                <div class="section-header">
                    <h2>Enterprise Patterns</h2>
                    <p>Patterns cho Domain Logic và Data Source trong enterprise applications</p>
                </div>

                <div class="patterns-tabs">
                    <button class="tab-button active" data-tab="domain-logic">Domain Logic</button>
                    <button class="tab-button" data-tab="data-source">Data Source</button>
                </div>

                <div class="tab-content">
                    <div id="domain-logic-patterns" class="tab-pane active">
                        <div class="patterns-grid"></div>
                    </div>
                    <div id="data-source-patterns" class="tab-pane">
                        <div class="patterns-grid"></div>
                    </div>
                </div>
            </section>

            <!-- Integration Patterns Section -->
            <section id="integration-patterns" class="content-section">
                <div class="section-header">
                    <h2>Integration Patterns</h2>
                    <p>Patterns cho Messaging và Endpoints integration</p>
                </div>

                <div class="patterns-tabs">
                    <button class="tab-button active" data-tab="messaging">Messaging</button>
                    <button class="tab-button" data-tab="endpoints">Endpoints</button>
                </div>

                <div class="tab-content">
                    <div id="messaging-patterns" class="tab-pane active">
                        <div class="patterns-grid"></div>
                    </div>
                    <div id="endpoints-patterns" class="tab-pane">
                        <div class="patterns-grid"></div>
                    </div>
                </div>
            </section>

            <!-- Implementation Section -->
            <section id="implementation" class="content-section">
                <div class="section-header">
                    <h2>Hướng dẫn Triển khai</h2>
                    <p>Framework lifecycle, hooks và best practices</p>
                </div>

                <div class="implementation-tabs">
                    <button class="tab-button active" data-tab="lifecycle">Framework Lifecycle</button>
                    <button class="tab-button" data-tab="extensions">Extension Points</button>
                    <button class="tab-button" data-tab="best-practices">Best Practices</button>
                </div>

                <div class="tab-content">
                    <div id="lifecycle" class="tab-pane active">
                        <div class="lifecycle-container">
                            <div class="lifecycle-section">
                                <h3>Boot Sequence</h3>
                                <div class="sequence-flow" id="boot-sequence"></div>
                            </div>
                            <div class="lifecycle-section">
                                <h3>Request Lifecycle</h3>
                                <div class="sequence-flow" id="request-lifecycle"></div>
                            </div>
                            <div class="lifecycle-section">
                                <h3>Hooks & Events</h3>
                                <div class="sequence-flow" id="hooks-events"></div>
                            </div>
                        </div>
                    </div>
                    <div id="extensions" class="tab-pane">
                        <div class="extensions-grid" id="extension-points"></div>
                    </div>
                    <div id="best-practices" class="tab-pane">
                        <div class="practices-categories" id="best-practices-content"></div>
                    </div>
                </div>
            </section>

            <!-- Decision Framework Section -->
            <section id="decision-framework" class="content-section">
                <div class="section-header">
                    <h2>Framework Quyết định</h2>
                    <p>Hướng dẫn lựa chọn patterns và đánh giá kiến trúc</p>
                </div>

                <div class="decision-tabs">
                    <button class="tab-button active" data-tab="pattern-selection">Lựa chọn Pattern</button>
                    <button class="tab-button" data-tab="architecture-evaluation">Đánh giá Kiến trúc</button>
                </div>

                <div class="tab-content">
                    <div id="pattern-selection" class="tab-pane active">
                        <div class="decision-content">
                            <div class="factors-section">
                                <h3>Các yếu tố cần xem xét:</h3>
                                <div class="factors-grid" id="selection-factors"></div>
                            </div>
                            <div class="process-section">
                                <h3>Quy trình lựa chọn:</h3>
                                <div class="process-flow" id="selection-process"></div>
                            </div>
                        </div>
                    </div>
                    <div id="architecture-evaluation" class="tab-pane">
                        <div class="decision-content">
                            <div class="criteria-section">
                                <h3>Tiêu chí đánh giá:</h3>
                                <div class="criteria-grid" id="evaluation-criteria"></div>
                            </div>
                            <div class="methods-section">
                                <h3>Phương pháp đánh giá:</h3>
                                <div class="methods-grid" id="evaluation-methods"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Search Results Section -->
            <section id="search-results" class="content-section">
                <div class="section-header">
                    <h2>Kết quả Tìm kiếm</h2>
                    <p>Kết quả tìm kiếm trong toàn bộ knowledge base</p>
                </div>
                <div id="search-results-container" class="search-results-grid"></div>
            </section>
        </main>
    </div>

    <!-- Pattern Detail Modal -->
    <div id="pattern-modal" class="modal hidden">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title"></h3>
                <button id="modal-close" class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body"></div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>