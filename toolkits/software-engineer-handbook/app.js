// Toolkit Handbook - Interactive JavaScript (Fixed)

class ToolkitHandbook {
    constructor() {
        this.currentSection = 'home';
        this.currentTopic = '';
        this.currentView = 'overview';
        this.bookmarks = new Set();
        this.progress = {};
        this.searchIndex = [];
        this.isDarkMode = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeTheme();
        this.buildSearchIndex();
        this.updateProgress();
        this.loadBookmarks();
        this.loadProgress();
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.addEventListener('click', () => this.toggleTheme());

        // Logo navigation
        const headerTitle = document.querySelector('.header-title');
        headerTitle.addEventListener('click', () => this.showHome());
        headerTitle.style.cursor = 'pointer';

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.querySelector('.search-btn');
        
        searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch(e.target.value);
            }
        });
        searchBtn.addEventListener('click', () => this.handleSearch(searchInput.value));

        // Navigation
        document.querySelectorAll('.nav-header').forEach(header => {
            header.addEventListener('click', (e) => this.toggleNavSection(e.currentTarget));
        });

        document.querySelectorAll('.nav-subitem').forEach(item => {
            item.addEventListener('click', (e) => this.navigateToTopic(e.currentTarget));
        });

        document.querySelectorAll('.overview-card').forEach(card => {
            card.addEventListener('click', (e) => this.navigateToSection(e.currentTarget));
        });

        // View tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchView(e.currentTarget));
        });

        // Bookmark button
        const bookmarkBtn = document.getElementById('bookmarkBtn');
        if (bookmarkBtn) {
            bookmarkBtn.addEventListener('click', () => this.toggleBookmark());
        }

        // Modal functionality
        const modal = document.getElementById('codeModal');
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => this.closeModal());
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        // Copy functionality
        const copyBtn = document.getElementById('copyBtn');
        copyBtn.addEventListener('click', () => this.copyCode());

        // Mobile navigation
        this.setupMobileNav();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    initializeTheme() {
        const savedTheme = localStorage.getItem('unifiedToolkit.theme') || 'light';
        this.setTheme(savedTheme);
        
        // Listen for theme changes from parent (unified toolkit)
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'THEME_CHANGE') {
                this.setTheme(event.data.theme);
            }
        });
    }

    toggleTheme() {
        const newTheme = this.isDarkMode ? 'light' : 'dark';
        this.setTheme(newTheme);
        
        // Broadcast theme change to parent (unified toolkit)
        if (window.parent !== window) {
            try {
                window.parent.postMessage({
                    type: 'THEME_CHANGE',
                    theme: newTheme
                }, '*');
            } catch (e) {
                // Ignore cross-origin errors
            }
        }
    }

    setTheme(theme) {
        this.isDarkMode = theme === 'dark';
        document.documentElement.setAttribute('data-color-scheme', theme);
        
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.textContent = this.isDarkMode ? '☀️' : '🌙';
        
        localStorage.setItem('unifiedToolkit.theme', theme);
    }

    buildSearchIndex() {
        this.searchIndex = [
            // Enterprise Platform Architecture
            { section: 'enterprise_platform', topic: 'system-design', title: 'System Design', content: 'scalability load balancing caching database design microservices architecture' },
            { section: 'enterprise_platform', topic: 'clean-architecture', title: 'Clean Architecture + DDD', content: 'domain driven design clean architecture dependency inversion entities use cases' },
            { section: 'enterprise_platform', topic: 'microservices', title: 'Microservices', content: 'microservices service mesh api gateway distributed systems docker kubernetes' },
            { section: 'enterprise_platform', topic: 'ai-native', title: 'AI-Native Design', content: 'machine learning integration ai pipelines model deployment feature stores' },

            // Programming Languages
            { section: 'programming_languages', topic: 'javascript', title: 'JavaScript/TypeScript', content: 'javascript typescript nodejs react async await promises closures' },
            { section: 'programming_languages', topic: 'python', title: 'Python', content: 'python django flask pandas numpy machine learning data science' },
            { section: 'programming_languages', topic: 'go', title: 'Go', content: 'golang goroutines channels concurrency performance web servers' },
            { section: 'programming_languages', topic: 'cpp-rust', title: 'C++/Rust', content: 'c++ rust memory management performance system programming safety' },
            { section: 'programming_languages', topic: 'language-comparison', title: 'Language Comparison', content: 'performance comparison ecosystem learning curve use cases' },

            // OOP & Design Patterns
            { section: 'oop_patterns', topic: 'solid', title: 'SOLID Principles', content: 'single responsibility open closed liskov substitution interface segregation dependency inversion' },
            { section: 'oop_patterns', topic: 'gof', title: 'Gang of Four Patterns', content: 'singleton factory observer strategy adapter decorator facade' },
            { section: 'oop_patterns', topic: 'arch-patterns', title: 'Architectural Patterns', content: 'mvc mvp mvvm clean architecture hexagonal architecture' },
            { section: 'oop_patterns', topic: 'best-practices', title: 'Best Practices', content: 'code review tdd ci cd documentation performance optimization' },

            // Data Structures & Algorithms
            { section: 'data_structures', topic: 'linear', title: 'Linear Structures', content: 'array linked list stack queue deque' },
            { section: 'data_structures', topic: 'nonlinear', title: 'Non-Linear Structures', content: 'tree binary tree graph hash table heap' },
            { section: 'data_structures', topic: 'algorithms', title: 'Algorithms', content: 'sorting searching dynamic programming greedy divide and conquer' },
            { section: 'data_structures', topic: 'complexity', title: 'Complexity Analysis', content: 'big o notation time complexity space complexity asymptotic analysis' },

            // Database & SQL
            { section: 'database', topic: 'relational', title: 'Relational Databases', content: 'sql acid properties normalization indexing transactions' },
            { section: 'database', topic: 'nosql', title: 'NoSQL Databases', content: 'mongodb redis cassandra document store key value graph database' },
            { section: 'database', topic: 'db-design', title: 'Database Design', content: 'schema design normalization denormalization indexing optimization' },
            { section: 'database', topic: 'db-optimization', title: 'Database Optimization', content: 'query optimization indexing performance tuning caching' },

            // DevOps & Cloud
            { section: 'devops', topic: 'cloud-fundamentals', title: 'Cloud Fundamentals', content: 'aws azure gcp iaas paas saas auto scaling load balancing' },
            { section: 'devops', topic: 'cicd', title: 'CI/CD', content: 'continuous integration deployment jenkins github actions docker kubernetes' },
            { section: 'devops', topic: 'monitoring', title: 'Monitoring', content: 'prometheus grafana logging metrics alerting observability' },
            { section: 'devops', topic: 'security', title: 'Security', content: 'authentication authorization encryption ssl tls security best practices' },

            // APIs & Communication
            { section: 'apis', topic: 'rest', title: 'REST APIs', content: 'http methods status codes restful design api versioning authentication' },
            { section: 'apis', topic: 'graphql', title: 'GraphQL', content: 'graphql queries mutations subscriptions schema federation' },
            { section: 'apis', topic: 'grpc', title: 'gRPC', content: 'grpc protocol buffers microservices rpc streaming' },
            { section: 'apis', topic: 'realtime', title: 'Real-time Communication', content: 'websockets server sent events socket io real time messaging' },

            // AI/ML
            { section: 'ai_ml', topic: 'math-foundations', title: 'Math Foundations', content: 'linear algebra calculus statistics probability optimization' },
            { section: 'ai_ml', topic: 'ml-algorithms', title: 'ML Algorithms', content: 'supervised learning unsupervised learning neural networks deep learning' },
            { section: 'ai_ml', topic: 'deep-learning', title: 'Deep Learning', content: 'neural networks cnn rnn transformer attention mechanism' },
            { section: 'ai_ml', topic: 'mlops', title: 'MLOps', content: 'model deployment monitoring versioning pipeline automation' },

            // Soft Skills & Thinking
            { section: 'soft_skills', topic: 'systems-thinking', title: 'Systems Thinking', content: 'holistic view feedback loops mental models root cause analysis' },
            { section: 'soft_skills', topic: 'problem-solving', title: 'Problem Solving', content: 'analytical thinking debugging troubleshooting decision making' },
            { section: 'soft_skills', topic: 'communication', title: 'Communication', content: 'technical writing documentation presentation skills stakeholder management' },
            { section: 'soft_skills', topic: 'leadership', title: 'Leadership', content: 'team leadership mentoring project management agile scrum' }
        ];
    }

    handleSearch(query) {
        if (!query.trim()) {
            this.clearSearch();
            return;
        }

        const results = this.searchIndex.filter(item => 
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.content.toLowerCase().includes(query.toLowerCase())
        );

        this.displaySearchResults(results, query);
    }

    displaySearchResults(results, query) {
        if (results.length === 0) {
            this.showNotification('Không tìm thấy kết quả nào 🔍');
            return;
        }

        // Show search results section
        this.currentSection = 'search';
        
        // Hide home, show section content
        document.getElementById('home').classList.remove('active');
        document.getElementById('section-content').classList.add('active');
        
        // Update title and breadcrumb
        const title = document.getElementById('section-title');
        title.textContent = `🔍 Kết quả tìm kiếm: "${query}"`;
        
        const breadcrumb = document.getElementById('breadcrumb');
        breadcrumb.innerHTML = `
            <span class="breadcrumb-item"><a href="#" onclick="app.showHome()">🏠 Trang chủ</a></span>
            <span class="breadcrumb-item active">🔍 Tìm kiếm</span>
        `;
        
        // Generate search results HTML
        const content = document.getElementById('dynamic-content');
        content.innerHTML = `
            <div class="search-results">
                <p>Tìm thấy ${results.length} kết quả cho "<strong>${query}</strong>"</p>
                <div class="content-grid">
                    ${results.map(result => `
                        <div class="content-card search-result-card" onclick="app.navigateToTopicFromSearch('${result.section}', '${result.topic}')">
                            <h3>${result.title}</h3>
                            <p>${this.highlightSearchTerm(result.content, query)}</p>
                            <div class="search-meta">
                                <span class="section-badge">${this.getSectionData(result.section).icon} ${this.getSectionData(result.section).title}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        this.showNotification(`Tìm thấy ${results.length} kết quả! 🎯`);
    }

    highlightSearchTerm(text, term) {
        const regex = new RegExp(`(${term})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    navigateToTopicFromSearch(section, topic) {
        // Find and click the corresponding nav item
        const navSection = document.querySelector(`[data-section="${section}"]`);
        if (navSection && !navSection.classList.contains('expanded')) {
            navSection.classList.add('expanded');
        }
        
        const navItem = document.querySelector(`[data-topic="${topic}"]`);
        if (navItem) {
            navItem.click();
        }
    }

    clearSearch() {
        // Clear search results if showing
        if (this.currentSection === 'search') {
            this.showHome();
        }
    }

    toggleNavSection(header) {
        const navItem = header.parentElement;
        const isExpanded = navItem.classList.contains('expanded');
        
        // Close other sections
        document.querySelectorAll('.nav-item.expanded').forEach(item => {
            if (item !== navItem) item.classList.remove('expanded');
        });
        
        navItem.classList.toggle('expanded', !isExpanded);
    }

    navigateToSection(card) {
        const section = card.getAttribute('data-section');
        this.showSection(section);
    }

    navigateToTopic(item) {
        const topic = item.getAttribute('data-topic');
        const section = item.closest('.nav-item').getAttribute('data-section');
        
        // Update active states
        document.querySelectorAll('.nav-subitem.active').forEach(i => i.classList.remove('active'));
        item.classList.add('active');
        
        this.currentSection = section;
        this.currentTopic = topic;
        this.showSection(section, topic);
    }

    showSection(section, topic = null) {
        this.currentSection = section;
        this.currentTopic = topic || '';
        
        // Hide home, show section content
        document.getElementById('home').classList.remove('active');
        document.getElementById('section-content').classList.add('active');
        
        // Update breadcrumb
        this.updateBreadcrumb(section, topic);
        
        // Load section content
        this.loadSectionContent(section, topic);
        
        // Update progress
        this.markAsVisited(section, topic);
        this.updateProgress();
        
        // Reset view to overview
        this.currentView = 'overview';
        this.updateActiveTab();
    }

    updateBreadcrumb(section, topic = null) {
        const breadcrumb = document.getElementById('breadcrumb');
        const sectionData = this.getSectionData(section);
        
        let html = '<span class="breadcrumb-item"><a href="#" onclick="app.showHome()">🏠 Trang chủ</a></span>';
        html += `<span class="breadcrumb-item active">${sectionData.icon} ${sectionData.title}</span>`;
        
        if (topic) {
            const topicTitle = this.getTopicTitle(section, topic);
            html += `<span class="breadcrumb-item active">${topicTitle}</span>`;
        }
        
        breadcrumb.innerHTML = html;
    }

    loadSectionContent(section, topic = null) {
        const title = document.getElementById('section-title');
        const content = document.getElementById('dynamic-content');
        const sectionData = this.getSectionData(section);
        
        title.textContent = `${sectionData.icon} ${sectionData.title}`;
        
        if (topic) {
            this.loadTopicContent(section, topic, content);
        } else {
            this.loadSectionOverview(section, content);
        }
        
        // Add fade-in animation
        content.classList.add('fade-in');
        setTimeout(() => content.classList.remove('fade-in'), 300);
    }

    loadTopicContent(section, topic, container) {
        const contentData = this.getTopicContentData(section, topic);
        container.innerHTML = this.generateTopicHTML(contentData);
        this.setupContentInteractions(container);
        
        // Update bookmark button state
        this.updateBookmarkButton();
    }

    loadSectionOverview(section, container) {
        const sectionData = this.getSectionData(section);
        const overviewData = this.getSectionOverviewData(section);
        
        container.innerHTML = `
            <div class="content-grid">
                ${overviewData.map(item => `
                    <div class="content-card" onclick="app.navigateToTopicFromCard('${section}', '${item.id}')">
                        <h3>${item.title}</h3>
                        <p>${item.description}</p>
                        <div class="difficulty-badge">${item.difficulty}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    updateBookmarkButton() {
        const key = `${this.currentSection}-${this.currentTopic}`;
        const btn = document.getElementById('bookmarkBtn');
        if (btn) {
            if (this.bookmarks.has(key)) {
                btn.classList.add('bookmarked');
            } else {
                btn.classList.remove('bookmarked');
            }
        }
    }

    updateActiveTab() {
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-view') === this.currentView) {
                btn.classList.add('active');
            }
        });
    }

    getSectionData(section) {
        const sections = {
            enterprise_platform: { title: 'Enterprise Platform Architecture', icon: '🏗️' },
            programming_languages: { title: 'Programming Languages', icon: '💻' },
            oop_patterns: { title: 'OOP & Design Patterns', icon: '🎨' },
            data_structures: { title: 'Data Structures & Algorithms', icon: '🔗' },
            database: { title: 'Database & SQL', icon: '💾' },
            devops: { title: 'DevOps & Cloud', icon: '☁️' },
            apis: { title: 'APIs & Communication', icon: '🌐' },
            ai_ml: { title: 'Machine Learning/AI', icon: '🤖' },
            soft_skills: { title: 'Thinking & Soft Skills', icon: '🧠' }
        };
        return sections[section] || { title: 'Unknown', icon: '❓' };
    }

    getSectionOverviewData(section) {
        const overviewData = {
            enterprise_platform: [
                { id: 'system-design', title: 'System Design', description: 'Scalability, Load Balancing, Caching', difficulty: 'Nâng cao' },
                { id: 'clean-architecture', title: 'Clean Architecture + DDD', description: 'Domain-Driven Design, Clean Code', difficulty: 'Nâng cao' },
                { id: 'microservices', title: 'Microservices', description: 'Distributed Systems, Service Mesh', difficulty: 'Nâng cao' },
                { id: 'ai-native', title: 'AI-Native Design', description: 'ML Integration, AI Pipelines', difficulty: 'Nâng cao' }
            ],
            programming_languages: [
                { id: 'javascript', title: 'JavaScript/TypeScript', description: 'Modern JS, Node.js, React, Async Programming', difficulty: 'Cơ bản' },
                { id: 'python', title: 'Python', description: 'Data Science, Web Development, ML Libraries', difficulty: 'Cơ bản' },
                { id: 'go', title: 'Go', description: 'Concurrency, Performance, Web Servers', difficulty: 'Trung cấp' },
                { id: 'cpp-rust', title: 'C++/Rust', description: 'System Programming, Memory Management', difficulty: 'Nâng cao' },
                { id: 'language-comparison', title: 'Language Comparison', description: 'Performance, Use Cases, Ecosystem Analysis', difficulty: 'Trung cấp' }
            ],
            oop_patterns: [
                { id: 'solid', title: 'SOLID Principles', description: 'Clean Code Design Principles', difficulty: 'Trung cấp' },
                { id: 'gof', title: 'Gang of Four Patterns', description: 'Classical Design Patterns', difficulty: 'Trung cấp' },
                { id: 'arch-patterns', title: 'Architectural Patterns', description: 'MVC, MVP, MVVM, Clean Architecture', difficulty: 'Nâng cao' },
                { id: 'best-practices', title: 'Best Practices', description: 'Code Quality, Refactoring', difficulty: 'Trung cấp' }
            ],
            data_structures: [
                { id: 'linear', title: 'Linear Structures', description: 'Array, Linked List, Stack, Queue', difficulty: 'Cơ bản' },
                { id: 'nonlinear', title: 'Non-Linear Structures', description: 'Tree, Graph, Hash Table, Heap', difficulty: 'Trung cấp' },
                { id: 'algorithms', title: 'Algorithms', description: 'Sorting, Searching, Dynamic Programming', difficulty: 'Trung cấp' },
                { id: 'complexity', title: 'Complexity Analysis', description: 'Big O Notation, Time/Space Complexity', difficulty: 'Cơ bản' }
            ],
            database: [
                { id: 'relational', title: 'Relational Databases', description: 'SQL, ACID, Normalization, Indexing', difficulty: 'Trung cấp' },
                { id: 'nosql', title: 'NoSQL Databases', description: 'MongoDB, Redis, Document/Key-Value Stores', difficulty: 'Trung cấp' },
                { id: 'db-design', title: 'Database Design', description: 'Schema Design, Optimization, Best Practices', difficulty: 'Nâng cao' },
                { id: 'db-optimization', title: 'Database Optimization', description: 'Query Tuning, Performance, Caching', difficulty: 'Nâng cao' }
            ],
            devops: [
                { id: 'cloud-fundamentals', title: 'Cloud Fundamentals', description: 'AWS, Azure, GCP, IaaS/PaaS/SaaS', difficulty: 'Trung cấp' },
                { id: 'cicd', title: 'CI/CD', description: 'Jenkins, GitHub Actions, Docker, Kubernetes', difficulty: 'Nâng cao' },
                { id: 'monitoring', title: 'Monitoring', description: 'Prometheus, Grafana, Logging, Alerting', difficulty: 'Nâng cao' },
                { id: 'security', title: 'Security', description: 'Authentication, Authorization, Encryption', difficulty: 'Nâng cao' }
            ],
            apis: [
                { id: 'rest', title: 'REST APIs', description: 'HTTP Methods, Status Codes, API Design', difficulty: 'Cơ bản' },
                { id: 'graphql', title: 'GraphQL', description: 'Queries, Mutations, Schema Design', difficulty: 'Trung cấp' },
                { id: 'grpc', title: 'gRPC', description: 'Protocol Buffers, Microservices, RPC', difficulty: 'Nâng cao' },
                { id: 'realtime', title: 'Real-time Communication', description: 'WebSockets, Server-Sent Events, Messaging', difficulty: 'Trung cấp' }
            ],
            ai_ml: [
                { id: 'math-foundations', title: 'Math Foundations', description: 'Linear Algebra, Calculus, Statistics', difficulty: 'Nâng cao' },
                { id: 'ml-algorithms', title: 'ML Algorithms', description: 'Supervised/Unsupervised Learning, Neural Networks', difficulty: 'Nâng cao' },
                { id: 'deep-learning', title: 'Deep Learning', description: 'CNN, RNN, Transformers, Attention', difficulty: 'Nâng cao' },
                { id: 'mlops', title: 'MLOps', description: 'Model Deployment, Monitoring, Pipelines', difficulty: 'Nâng cao' }
            ],
            soft_skills: [
                { id: 'systems-thinking', title: 'Systems Thinking', description: 'Holistic View, Feedback Loops, Mental Models', difficulty: 'Trung cấp' },
                { id: 'problem-solving', title: 'Problem Solving', description: 'Analytical Thinking, Debugging, Decision Making', difficulty: 'Cơ bản' },
                { id: 'communication', title: 'Communication', description: 'Technical Writing, Documentation, Presentations', difficulty: 'Cơ bản' },
                { id: 'leadership', title: 'Leadership', description: 'Team Leadership, Mentoring, Project Management', difficulty: 'Trung cấp' }
            ]
        };
        
        return overviewData[section] || [];
    }

    getTopicContentData(section, topic) {
        // Comprehensive topic content data
        const contentDatabase = {
            'enterprise_platform': {
                'system-design': {
                    title: 'System Design',
                    overview: 'System Design là quá trình thiết kế kiến trúc, thành phần, giao diện và dữ liệu cho một hệ thống để đáp ứng các yêu cầu cụ thể.',
                    keyPoints: [
                        'Scalability (Khả năng mở rộng)',
                        'Reliability (Độ tin cậy)',
                        'Availability (Tính khả dụng)',
                        'Consistency (Tính nhất quán)',
                        'Performance (Hiệu suất)'
                    ],
                    details: {
                        'Scalability': 'Khả năng xử lý tải tăng thông qua horizontal scaling (thêm server) hoặc vertical scaling (nâng cấp hardware)',
                        'Load Balancing': 'Phân phối requests đều trên nhiều servers để tối ưu hiệu suất',
                        'Caching': 'Lưu trữ tạm thời dữ liệu để giảm thời gian truy cập (Redis, Memcached)',
                        'Database Design': 'Thiết kế schema, indexing, partitioning, replication',
                        'CAP Theorem': 'Chỉ có thể đảm bảo 2 trong 3: Consistency, Availability, Partition tolerance'
                    },
                    examples: {
                        'Load Balancer Config': `# Nginx Load Balancer Configuration
upstream backend {
    server ************:3000;
    server ************:3000;
    server ************:3000;
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}`,
                        'Caching Strategy': `// Redis Caching Example
const redis = require('redis');
const client = redis.createClient();

async function getCachedData(key) {
    try {
        const cached = await client.get(key);
        if (cached) {
            return JSON.parse(cached);
        }
        
        // Fetch from database
        const data = await fetchFromDatabase(key);
        
        // Cache for 1 hour
        await client.setex(key, 3600, JSON.stringify(data));
        
        return data;
    } catch (error) {
        console.error('Cache error:', error);
        return await fetchFromDatabase(key);
    }
}`
                    },
                    practice: [
                        'Thiết kế một hệ thống chat real-time cho 1 triệu users',
                        'Tạo kiến trúc cho một e-commerce platform',
                        'Thiết kế system để handle 100k requests/second'
                    ]
                },
                'clean-architecture': {
                    title: 'Clean Architecture + DDD',
                    overview: 'Clean Architecture kết hợp Domain-Driven Design tạo ra kiến trúc phần mềm dễ maintain, test và scale.',
                    keyPoints: [
                        'Domain Layer (Entities, Value Objects)',
                        'Application Layer (Use Cases)',
                        'Infrastructure Layer (Database, External APIs)',
                        'Presentation Layer (Controllers, Views)',
                        'Dependency Inversion Principle'
                    ],
                    details: {
                        'Entities': 'Business objects chứa logic core của domain',
                        'Use Cases': 'Application-specific business rules',
                        'Repository Pattern': 'Abstraction layer cho data access',
                        'Dependency Injection': 'Invert dependencies để dễ test',
                        'Bounded Context': 'Phân chia domain thành các context riêng biệt'
                    },
                    examples: {
                        'Entity Example': `// Domain Entity
class User {
    constructor(id, email, password) {
        this.id = id;
        this.email = email;
        this.password = password;
        this.createdAt = new Date();
    }
    
    changeEmail(newEmail) {
        if (!this.isValidEmail(newEmail)) {
            throw new Error('Invalid email');
        }
        this.email = newEmail;
    }
    
    isValidEmail(email) {
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        return emailRegex.test(email);
    }
}`,
                        'Use Case Example': `// Application Use Case
class CreateUserUseCase {
    constructor(userRepository, emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }
    
    async execute(userData) {
        // Business logic
        const user = new User(
            generateId(),
            userData.email,
            hashPassword(userData.password)
        );
        
        // Check business rules
        if (await this.userRepository.existsByEmail(user.email)) {
            throw new Error('Email already exists');
        }
        
        // Save user
        const savedUser = await this.userRepository.save(user);
        
        // Send welcome email
        await this.emailService.sendWelcomeEmail(savedUser);
        
        return savedUser;
    }
}`
                    }
                }
            },
            'programming_languages': {
                'javascript': {
                    title: 'JavaScript/TypeScript',
                    overview: 'JavaScript là ngôn ngữ lập trình dynamic, TypeScript thêm static typing để cải thiện developer experience.',
                    keyPoints: [
                        'ES6+ Features (Arrow functions, Destructuring, Modules)',
                        'Async Programming (Promises, async/await)',
                        'TypeScript Type System',
                        'Node.js Backend Development',
                        'React/Vue.js Frontend Development'
                    ],
                    details: {
                        'Closures': 'Functions có access đến variables từ outer scope',
                        'Prototypal Inheritance': 'Object inheritance thông qua prototype chain',
                        'Event Loop': 'Cơ chế xử lý asynchronous operations',
                        'Type Guards': 'TypeScript techniques để narrow types',
                        'Module System': 'ES6 modules, CommonJS, AMD'
                    },
                    examples: {
                        'Modern JavaScript': `// ES6+ Features
const users = [
    { name: 'John', age: 25 },
    { name: 'Jane', age: 30 },
    { name: 'Bob', age: 35 }
];

// Destructuring & Arrow Functions
const getAdults = (users) => 
    users.filter(({ age }) => age >= 18)
         .map(({ name, age }) => ({ name, age }));

// Async/Await
async function fetchUserData(userId) {
    try {
        const response = await fetch(\`/api/users/\${userId}\`);
        const userData = await response.json();
        return userData;
    } catch (error) {
        console.error('Failed to fetch user:', error);
        throw error;
    }
}`,
                        'TypeScript Advanced': `// Generic Utility Types
interface User {
    id: number;
    name: string;
    email: string;
    password: string;
}

// Pick specific properties
type PublicUser = Pick<User, 'id' | 'name' | 'email'>;

// Make all properties optional
type PartialUser = Partial<User>;

// Generic function with constraints
function processEntity<T extends { id: number }>(
    entity: T,
    processor: (item: T) => T
): T {
    console.log(\`Processing entity \${entity.id}\`);
    return processor(entity);
}

// Conditional types
type ApiResponse<T> = T extends string 
    ? { message: T }
    : { data: T };`
                    }
                },
                'python': {
                    title: 'Python',
                    overview: 'Python là ngôn ngữ lập trình high-level, interpreted với syntax đơn giản và thư viện phong phú, đặc biệt mạnh trong Data Science, Web Development và Machine Learning.',
                    keyPoints: [
                        'Syntax đơn giản, dễ đọc (Pythonic code)',
                        'Thư viện phong phú (NumPy, Pandas, Django, Flask)',
                        'Interpreted language với REPL interactive',
                        'Duck typing và dynamic typing',
                        'Powerful data structures (list, dict, set, tuple)'
                    ],
                    details: {
                        'Data Types': 'int, float, str, bool, list, tuple, dict, set với automatic memory management',
                        'List Comprehensions': 'Cách viết ngắn gọn để tạo lists: [x**2 for x in range(10) if x%2==0]',
                        'Decorators': 'Functions modify other functions: @property, @staticmethod, @classmethod',
                        'Context Managers': 'with statement để manage resources automatically',
                        'Generators': 'yield keyword để tạo iterators memory-efficient'
                    },
                    examples: {
                        'Python Fundamentals': `# Python Core Features
# List comprehensions
numbers = [1, 2, 3, 4, 5]
squares = [x**2 for x in numbers if x % 2 == 0]
print(squares)  # [4, 16]

# Dictionary comprehensions
words = ['hello', 'world', 'python']
word_lengths = {word: len(word) for word in words}

# Generator function
def fibonacci_generator(n):
    a, b = 0, 1
    for _ in range(n):
        yield a
        a, b = b, a + b

# Context manager
class FileManager:
    def __init__(self, filename, mode):
        self.filename = filename
        self.mode = mode

    def __enter__(self):
        self.file = open(self.filename, self.mode)
        return self.file

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.file.close()

# Usage
with FileManager('data.txt', 'r') as f:
    content = f.read()`,
                        'Advanced Python': `# Decorators and Metaclasses
from functools import wraps
import time

def timing_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.4f} seconds")
        return result
    return wrapper

@timing_decorator
def slow_function():
    time.sleep(1)
    return "Done"

# Class with properties and methods
class DataProcessor:
    def __init__(self, data):
        self._data = data

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, value):
        if not isinstance(value, list):
            raise ValueError("Data must be a list")
        self._data = value

    @staticmethod
    def validate_data(data):
        return isinstance(data, list) and len(data) > 0

    @classmethod
    def from_csv(cls, filename):
        import csv
        with open(filename, 'r') as f:
            reader = csv.reader(f)
            data = list(reader)
        return cls(data)

# Multiple inheritance and super()
class Mixin:
    def log(self, message):
        print(f"[LOG] {message}")

class Calculator(Mixin):
    def add(self, a, b):
        result = a + b
        self.log(f"Added {a} + {b} = {result}")
        return result`
                    },
                    practice: [
                        'Xây dựng một web scraper sử dụng requests và BeautifulSoup',
                        'Tạo REST API với FastAPI và SQLAlchemy',
                        'Phân tích dữ liệu với Pandas và tạo visualization với Matplotlib',
                        'Implement design patterns: Singleton, Factory, Observer trong Python'
                    ]
                },
                'go': {
                    title: 'Go (Golang)',
                    overview: 'Go là ngôn ngữ lập trình compiled, statically typed được Google phát triển, nổi tiếng về performance, concurrency và simplicity.',
                    keyPoints: [
                        'Compiled language với fast compilation',
                        'Built-in concurrency với goroutines và channels',
                        'Garbage collection tự động',
                        'Simple syntax, no inheritance',
                        'Excellent for microservices và system programming'
                    ],
                    details: {
                        'Goroutines': 'Lightweight threads managed by Go runtime, có thể tạo hàng triệu goroutines',
                        'Channels': 'Communication mechanism giữa goroutines: "Don\'t communicate by sharing memory; share memory by communicating"',
                        'Interfaces': 'Implicit implementation, duck typing cho static language',
                        'Error Handling': 'Explicit error handling với multiple return values',
                        'Package System': 'Simple import system với go modules'
                    },
                    examples: {
                        'Go Concurrency': `package main

import (
    "fmt"
    "sync"
    "time"
)

// Worker pool pattern
func worker(id int, jobs <-chan int, results chan<- int, wg *sync.WaitGroup) {
    defer wg.Done()
    for job := range jobs {
        fmt.Printf("Worker %d processing job %d\\n", id, job)
        time.Sleep(time.Second) // Simulate work
        results <- job * 2
    }
}

func main() {
    const numWorkers = 3
    const numJobs = 5

    jobs := make(chan int, numJobs)
    results := make(chan int, numJobs)

    var wg sync.WaitGroup

    // Start workers
    for w := 1; w <= numWorkers; w++ {
        wg.Add(1)
        go worker(w, jobs, results, &wg)
    }

    // Send jobs
    for j := 1; j <= numJobs; j++ {
        jobs <- j
    }
    close(jobs)

    // Wait for workers to finish
    go func() {
        wg.Wait()
        close(results)
    }()

    // Collect results
    for result := range results {
        fmt.Printf("Result: %d\\n", result)
    }
}`,
                        'Go Web Server': `package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "strconv"
    "github.com/gorilla/mux"
)

type User struct {
    ID   int    \`json:"id"\`
    Name string \`json:"name"\`
    Email string \`json:"email"\`
}

var users []User
var nextID = 1

func getUsers(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(users)
}

func createUser(w http.ResponseWriter, r *http.Request) {
    var user User
    if err := json.NewDecoder(r.Body).Decode(&user); err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    user.ID = nextID
    nextID++
    users = append(users, user)

    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusCreated)
    json.NewEncoder(w).Encode(user)
}

func getUserByID(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    id, err := strconv.Atoi(vars["id"])
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }

    for _, user := range users {
        if user.ID == id {
            w.Header().Set("Content-Type", "application/json")
            json.NewEncoder(w).Encode(user)
            return
        }
    }

    http.Error(w, "User not found", http.StatusNotFound)
}

func main() {
    r := mux.NewRouter()

    r.HandleFunc("/users", getUsers).Methods("GET")
    r.HandleFunc("/users", createUser).Methods("POST")
    r.HandleFunc("/users/{id}", getUserByID).Methods("GET")

    fmt.Println("Server starting on :8080")
    log.Fatal(http.ListenAndServe(":8080", r))
}`
                    },
                    practice: [
                        'Xây dựng một concurrent web crawler',
                        'Tạo microservice với gRPC và Protocol Buffers',
                        'Implement rate limiter sử dụng channels',
                        'Xây dựng CLI tool với Cobra framework'
                    ]
                },
                'cpp-rust': {
                    title: 'C++/Rust',
                    overview: 'C++ và Rust là các ngôn ngữ system programming với performance cao. C++ có legacy lớn, Rust tập trung vào memory safety.',
                    keyPoints: [
                        'C++: Manual memory management, multiple paradigms',
                        'Rust: Memory safety without garbage collection',
                        'Zero-cost abstractions trong cả hai ngôn ngữ',
                        'Excellent performance cho system programming',
                        'C++: Mature ecosystem, Rust: Modern tooling'
                    ],
                    details: {
                        'Memory Management': 'C++: new/delete, smart pointers. Rust: ownership system, borrowing',
                        'Concurrency': 'C++: std::thread, std::async. Rust: fearless concurrency với ownership',
                        'Error Handling': 'C++: exceptions. Rust: Result<T, E> và Option<T>',
                        'Performance': 'Cả hai compile thành native code với optimization cao',
                        'Safety': 'C++: runtime errors có thể. Rust: compile-time safety guarantees'
                    },
                    examples: {
                        'C++ Modern Features': `#include <iostream>
#include <vector>
#include <memory>
#include <algorithm>
#include <future>

// RAII and Smart Pointers
class Resource {
private:
    std::string name;
public:
    Resource(const std::string& n) : name(n) {
        std::cout << "Resource " << name << " created\\n";
    }
    ~Resource() {
        std::cout << "Resource " << name << " destroyed\\n";
    }
    void use() { std::cout << "Using " << name << "\\n"; }
};

// Modern C++ with auto, lambdas, and algorithms
int main() {
    // Smart pointers
    auto resource = std::make_unique<Resource>("Database");
    resource->use();

    // Lambda expressions and algorithms
    std::vector<int> numbers = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};

    auto even_numbers = std::vector<int>{};
    std::copy_if(numbers.begin(), numbers.end(),
                 std::back_inserter(even_numbers),
                 [](int n) { return n % 2 == 0; });

    // Range-based for loop
    for (const auto& num : even_numbers) {
        std::cout << num << " ";
    }

    // Async programming
    auto future_result = std::async(std::launch::async, []() {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        return 42;
    });

    std::cout << "Result: " << future_result.get() << std::endl;

    return 0;
}`,
                        'Rust Ownership': `// Rust ownership and borrowing
use std::collections::HashMap;

#[derive(Debug)]
struct User {
    name: String,
    email: String,
    age: u32,
}

impl User {
    fn new(name: String, email: String, age: u32) -> Self {
        User { name, email, age }
    }

    fn greet(&self) -> String {
        format!("Hello, my name is {}", self.name)
    }

    fn have_birthday(&mut self) {
        self.age += 1;
    }
}

// Error handling with Result
fn divide(a: f64, b: f64) -> Result<f64, String> {
    if b == 0.0 {
        Err("Cannot divide by zero".to_string())
    } else {
        Ok(a / b)
    }
}

fn main() {
    // Ownership
    let user1 = User::new(
        "Alice".to_string(),
        "<EMAIL>".to_string(),
        25
    );

    // Borrowing (immutable reference)
    println!("{}", user1.greet());

    // Mutable borrowing
    let mut user2 = user1; // user1 is moved, no longer accessible
    user2.have_birthday();
    println!("{:?}", user2);

    // Error handling
    match divide(10.0, 2.0) {
        Ok(result) => println!("Result: {}", result),
        Err(error) => println!("Error: {}", error),
    }

    // Collections and iterators
    let numbers: Vec<i32> = (1..=10).collect();
    let sum: i32 = numbers
        .iter()
        .filter(|&&x| x % 2 == 0)
        .map(|&x| x * x)
        .sum();

    println!("Sum of squares of even numbers: {}", sum);

    // HashMap usage
    let mut scores = HashMap::new();
    scores.insert("Alice", 95);
    scores.insert("Bob", 87);

    for (name, score) in &scores {
        println!("{}: {}", name, score);
    }
}`
                    },
                    practice: [
                        'C++: Implement một thread-safe singleton pattern',
                        'Rust: Xây dựng command-line tool với clap crate',
                        'C++: Tạo custom allocator cho performance optimization',
                        'Rust: Implement async web server với Tokio và Warp'
                    ]
                },
                'language-comparison': {
                    title: 'So Sánh Ngôn Ngữ Lập Trình',
                    overview: 'Phân tích so sánh các ngôn ngữ lập trình phổ biến về performance, use cases, ecosystem và learning curve.',
                    keyPoints: [
                        'Performance: C++/Rust > Go > JavaScript/Python',
                        'Development Speed: Python > JavaScript > Go > C++/Rust',
                        'Memory Safety: Rust > Go/JavaScript/Python > C++',
                        'Ecosystem: JavaScript > Python > C++ > Go > Rust',
                        'Learning Curve: Python < JavaScript < Go < C++ < Rust'
                    ],
                    details: {
                        'Performance Comparison': 'Benchmarks cho CPU-intensive tasks: C++ (1x) → Rust (1.1x) → Go (2-3x) → JavaScript (5-10x) → Python (10-100x)',
                        'Use Case Matrix': 'Web: JS/Python, Systems: C++/Rust, Cloud/Microservices: Go, Data Science: Python, Mobile: JavaScript (React Native)',
                        'Memory Model': 'Manual (C++) vs Ownership (Rust) vs GC (Go/JS/Python)',
                        'Concurrency': 'Threads (C++) vs Async/Await (JS/Python) vs Goroutines (Go) vs Fearless (Rust)',
                        'Deployment': 'Compiled (C++/Rust/Go) vs Interpreted (Python) vs JIT (JavaScript)'
                    },
                    examples: {
                        'Performance Benchmark': `// Same algorithm in different languages

// JavaScript (Node.js)
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}
console.time('JS');
console.log(fibonacci(40));
console.timeEnd('JS');

# Python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

import time
start = time.time()
print(fibonacci(40))
print(f"Python: {time.time() - start:.4f}s")

// Go
package main
import (
    "fmt"
    "time"
)

func fibonacci(n int) int {
    if n <= 1 {
        return n
    }
    return fibonacci(n-1) + fibonacci(n-2)
}

func main() {
    start := time.Now()
    fmt.Println(fibonacci(40))
    fmt.Printf("Go: %v\\n", time.Since(start))
}

// Rust
use std::time::Instant;

fn fibonacci(n: u32) -> u32 {
    if n <= 1 {
        return n;
    }
    fibonacci(n - 1) + fibonacci(n - 2)
}

fn main() {
    let start = Instant::now();
    println!("{}", fibonacci(40));
    println!("Rust: {:?}", start.elapsed());
}`,
                        'Language Selection Matrix': `// Decision Matrix for Language Selection

const languageMatrix = {
    webDevelopment: {
        frontend: ['JavaScript', 'TypeScript'],
        backend: ['JavaScript/Node.js', 'Python', 'Go'],
        fullstack: ['JavaScript/TypeScript']
    },

    systemsProgramming: {
        os: ['C++', 'Rust', 'C'],
        embedded: ['C++', 'Rust', 'C'],
        drivers: ['C++', 'C']
    },

    dataScience: {
        analysis: ['Python', 'R'],
        ml: ['Python', 'Julia'],
        bigData: ['Python', 'Scala', 'Java']
    },

    cloudNative: {
        microservices: ['Go', 'Java', 'C#'],
        containers: ['Go', 'Python'],
        serverless: ['JavaScript', 'Python', 'Go']
    },

    mobile: {
        crossPlatform: ['JavaScript (React Native)', 'Dart (Flutter)'],
        ios: ['Swift'],
        android: ['Kotlin', 'Java']
    },

    gamesDevelopment: {
        aaa: ['C++'],
        indie: ['C#', 'JavaScript'],
        mobile: ['C#', 'JavaScript']
    }
};

// Performance vs Productivity Trade-off
const tradeoffMatrix = {
    'C++': { performance: 10, productivity: 4, safety: 3 },
    'Rust': { performance: 9, productivity: 5, safety: 10 },
    'Go': { performance: 7, productivity: 8, safety: 7 },
    'JavaScript': { performance: 5, productivity: 9, safety: 6 },
    'Python': { performance: 3, productivity: 10, safety: 7 }
};`
                    },
                    practice: [
                        'Implement cùng một algorithm trong 3 ngôn ngữ khác nhau và so sánh performance',
                        'Xây dựng cùng một REST API trong Python (FastAPI), Go (Gin), và JavaScript (Express)',
                        'So sánh memory usage và startup time của các ngôn ngữ',
                        'Phân tích ecosystem và package management của từng ngôn ngữ'
                    ]
                }
            },
            'data_structures': {
                'linear': {
                    title: 'Linear Data Structures',
                    overview: 'Cấu trúc dữ liệu tuyến tính là những cấu trúc mà các phần tử được sắp xếp theo thứ tự tuần tự.',
                    keyPoints: [
                        'Array - Mảng tĩnh với random access',
                        'Linked List - Danh sách liên kết động',
                        'Stack - LIFO (Last In, First Out)',
                        'Queue - FIFO (First In, First Out)',
                        'Deque - Double-ended queue'
                    ],
                    details: {
                        'Array': 'Fixed-size, O(1) access, O(n) insertion/deletion',
                        'Linked List': 'Dynamic size, O(n) access, O(1) insertion/deletion tại node',
                        'Stack': 'push(), pop(), top(), useful cho recursion, undo operations',
                        'Queue': 'enqueue(), dequeue(), front(), useful cho BFS, task scheduling',
                        'Deque': 'Có thể thêm/xóa từ cả hai đầu'
                    },
                    examples: {
                        'Stack Implementation': `class Stack {
    constructor() {
        this.items = [];
    }
    
    push(item) {
        this.items.push(item);
    }
    
    pop() {
        if (this.isEmpty()) {
            throw new Error('Stack is empty');
        }
        return this.items.pop();
    }
    
    peek() {
        if (this.isEmpty()) {
            return null;
        }
        return this.items[this.items.length - 1];
    }
    
    isEmpty() {
        return this.items.length === 0;
    }
    
    size() {
        return this.items.length;
    }
}

// Usage example
const stack = new Stack();
stack.push(1);
stack.push(2);
stack.push(3);
console.log(stack.pop()); // 3
console.log(stack.peek()); // 2`,
                        'Linked List Implementation': `class ListNode {
    constructor(val, next = null) {
        this.val = val;
        this.next = next;
    }
}

class LinkedList {
    constructor() {
        this.head = null;
        this.size = 0;
    }
    
    prepend(val) {
        const newNode = new ListNode(val, this.head);
        this.head = newNode;
        this.size++;
    }
    
    append(val) {
        const newNode = new ListNode(val);
        
        if (!this.head) {
            this.head = newNode;
        } else {
            let current = this.head;
            while (current.next) {
                current = current.next;
            }
            current.next = newNode;
        }
        this.size++;
    }
    
    delete(val) {
        if (!this.head) return false;
        
        if (this.head.val === val) {
            this.head = this.head.next;
            this.size--;
            return true;
        }
        
        let current = this.head;
        while (current.next && current.next.val !== val) {
            current = current.next;
        }
        
        if (current.next) {
            current.next = current.next.next;
            this.size--;
            return true;
        }
        
        return false;
    }
}`
                    },
                    practice: [
                        'Implement Stack sử dụng Linked List',
                        'Tạo Queue với circular array',
                        'Xây dựng Deque với doubly linked list',
                        'Solve problems: Valid Parentheses, Reverse Polish Notation'
                    ]
                },
                'nonlinear': {
                    title: 'Non-Linear Data Structures',
                    overview: 'Cấu trúc dữ liệu phi tuyến tính là những cấu trúc mà các phần tử không được sắp xếp theo thứ tự tuần tự.',
                    keyPoints: [
                        'Binary Tree - Cây nhị phân với hierarchical structure',
                        'Binary Search Tree - BST với ordered property',
                        'Graph - Vertices và edges, directed/undirected',
                        'Hash Table - Key-value pairs với O(1) access',
                        'Heap - Complete binary tree với heap property'
                    ],
                    details: {
                        'Binary Tree': 'Mỗi node có tối đa 2 children, traversal: inorder, preorder, postorder',
                        'BST': 'Left subtree < root < right subtree, search/insert/delete O(log n) average',
                        'Graph': 'Vertices connected by edges, representations: adjacency list/matrix',
                        'Hash Table': 'Hash function maps keys to indices, collision resolution: chaining/open addressing',
                        'Heap': 'Max heap: parent ≥ children, Min heap: parent ≤ children, priority queue implementation'
                    },
                    examples: {
                        'Binary Search Tree': `class TreeNode {
    constructor(val, left = null, right = null) {
        this.val = val;
        this.left = left;
        this.right = right;
    }
}

class BinarySearchTree {
    constructor() {
        this.root = null;
    }

    insert(val) {
        this.root = this._insertHelper(this.root, val);
    }

    _insertHelper(node, val) {
        if (!node) {
            return new TreeNode(val);
        }

        if (val < node.val) {
            node.left = this._insertHelper(node.left, val);
        } else if (val > node.val) {
            node.right = this._insertHelper(node.right, val);
        }

        return node;
    }

    search(val) {
        return this._searchHelper(this.root, val);
    }

    _searchHelper(node, val) {
        if (!node || node.val === val) {
            return node;
        }

        if (val < node.val) {
            return this._searchHelper(node.left, val);
        }

        return this._searchHelper(node.right, val);
    }

    inorderTraversal() {
        const result = [];
        this._inorderHelper(this.root, result);
        return result;
    }

    _inorderHelper(node, result) {
        if (node) {
            this._inorderHelper(node.left, result);
            result.push(node.val);
            this._inorderHelper(node.right, result);
        }
    }
}`,
                        'Hash Table Implementation': `class HashTable {
    constructor(size = 53) {
        this.keyMap = new Array(size);
    }

    _hash(key) {
        let total = 0;
        let WEIRD_PRIME = 31;
        for (let i = 0; i < Math.min(key.length, 100); i++) {
            let char = key[i];
            let value = char.charCodeAt(0) - 96;
            total = (total * WEIRD_PRIME + value) % this.keyMap.length;
        }
        return total;
    }

    set(key, value) {
        let index = this._hash(key);
        if (!this.keyMap[index]) {
            this.keyMap[index] = [];
        }

        // Check if key already exists
        for (let i = 0; i < this.keyMap[index].length; i++) {
            if (this.keyMap[index][i][0] === key) {
                this.keyMap[index][i][1] = value;
                return;
            }
        }

        this.keyMap[index].push([key, value]);
    }

    get(key) {
        let index = this._hash(key);
        if (this.keyMap[index]) {
            for (let i = 0; i < this.keyMap[index].length; i++) {
                if (this.keyMap[index][i][0] === key) {
                    return this.keyMap[index][i][1];
                }
            }
        }
        return undefined;
    }

    keys() {
        let keysArr = [];
        for (let i = 0; i < this.keyMap.length; i++) {
            if (this.keyMap[i]) {
                for (let j = 0; j < this.keyMap[i].length; j++) {
                    if (!keysArr.includes(this.keyMap[i][j][0])) {
                        keysArr.push(this.keyMap[i][j][0]);
                    }
                }
            }
        }
        return keysArr;
    }

    values() {
        let valuesArr = [];
        for (let i = 0; i < this.keyMap.length; i++) {
            if (this.keyMap[i]) {
                for (let j = 0; j < this.keyMap[i].length; j++) {
                    if (!valuesArr.includes(this.keyMap[i][j][1])) {
                        valuesArr.push(this.keyMap[i][j][1]);
                    }
                }
            }
        }
        return valuesArr;
    }
}`
                    },
                    practice: [
                        'Implement AVL Tree với self-balancing',
                        'Tạo Graph với BFS và DFS traversal',
                        'Xây dựng Min/Max Heap và Priority Queue',
                        'Solve: Lowest Common Ancestor, Graph Shortest Path'
                    ]
                }
            },
            'oop_patterns': {
                'solid': {
                    title: 'Nguyên Lý SOLID',
                    overview: 'SOLID là 5 nguyên tắc thiết kế phần mềm cốt lõi giúp tạo ra code dễ maintain, extend và test.',
                    keyPoints: [
                        'Single Responsibility Principle (SRP)',
                        'Open/Closed Principle (OCP)',
                        'Liskov Substitution Principle (LSP)',
                        'Interface Segregation Principle (ISP)',
                        'Dependency Inversion Principle (DIP)'
                    ],
                    details: {
                        'Single Responsibility': 'Một class chỉ nên có một lý do để thay đổi. Mỗi class chỉ đảm nhận một trách nhiệm duy nhất.',
                        'Open/Closed': 'Classes nên mở cho extension nhưng đóng cho modification. Sử dụng inheritance và polymorphism.',
                        'Liskov Substitution': 'Objects của derived class phải có thể thay thế objects của base class mà không làm hỏng chương trình.',
                        'Interface Segregation': 'Clients không nên bị buộc phụ thuộc vào interfaces mà chúng không sử dụng.',
                        'Dependency Inversion': 'High-level modules không nên phụ thuộc vào low-level modules. Cả hai nên phụ thuộc vào abstractions.'
                    },
                    examples: {
                        'SOLID Principles Example': `// Single Responsibility Principle
class User {
    constructor(name, email) {
        this.name = name;
        this.email = email;
    }
}

class UserValidator {
    static validate(user) {
        return user.email.includes('@');
    }
}

class UserRepository {
    save(user) {
        // Save to database
        console.log(\`Saving user: \${user.name}\`);
    }
}

// Open/Closed Principle
class Shape {
    area() {
        throw new Error('Must implement area method');
    }
}

class Rectangle extends Shape {
    constructor(width, height) {
        super();
        this.width = width;
        this.height = height;
    }

    area() {
        return this.width * this.height;
    }
}

class Circle extends Shape {
    constructor(radius) {
        super();
        this.radius = radius;
    }

    area() {
        return Math.PI * this.radius * this.radius;
    }
}

class AreaCalculator {
    calculateTotalArea(shapes) {
        return shapes.reduce((total, shape) => total + shape.area(), 0);
    }
}`,
                        'Dependency Inversion Example': `// Bad: High-level module depends on low-level module
class MySQLDatabase {
    save(data) {
        console.log('Saving to MySQL:', data);
    }
}

class UserService {
    constructor() {
        this.database = new MySQLDatabase(); // Tight coupling
    }

    createUser(userData) {
        // Business logic
        this.database.save(userData);
    }
}

// Good: Both depend on abstraction
class DatabaseInterface {
    save(data) {
        throw new Error('Must implement save method');
    }
}

class MySQLDatabase extends DatabaseInterface {
    save(data) {
        console.log('Saving to MySQL:', data);
    }
}

class PostgreSQLDatabase extends DatabaseInterface {
    save(data) {
        console.log('Saving to PostgreSQL:', data);
    }
}

class UserService {
    constructor(database) {
        this.database = database; // Dependency injection
    }

    createUser(userData) {
        // Business logic
        this.database.save(userData);
    }
}

// Usage
const mysqlDB = new MySQLDatabase();
const userService = new UserService(mysqlDB);`
                    },
                    practice: [
                        'Refactor một class lớn để tuân thủ Single Responsibility Principle',
                        'Thiết kế một hệ thống payment gateway sử dụng Open/Closed Principle',
                        'Implement dependency injection container đơn giản',
                        'Tạo interface segregation cho một e-commerce system'
                    ]
                },
                'gof': {
                    title: 'Gang of Four Patterns',
                    overview: '23 design patterns cổ điển được chia thành 3 nhóm: Creational, Structural, và Behavioral patterns.',
                    keyPoints: [
                        'Creational: Singleton, Factory, Builder, Prototype',
                        'Structural: Adapter, Decorator, Facade, Proxy',
                        'Behavioral: Observer, Strategy, Command, State',
                        'Giải quyết các vấn đề thiết kế phổ biến',
                        'Cung cấp vocabulary chung cho developers'
                    ],
                    details: {
                        'Creational Patterns': 'Xử lý object creation mechanisms, tăng flexibility và reuse của existing code',
                        'Structural Patterns': 'Giải thích cách assemble objects và classes thành larger structures',
                        'Behavioral Patterns': 'Quan tâm đến algorithms và assignment of responsibilities giữa objects',
                        'Pattern Selection': 'Chọn pattern dựa trên problem context, không áp dụng mù quáng',
                        'Anti-patterns': 'Tránh overuse patterns, đôi khi simple solution tốt hơn'
                    },
                    examples: {
                        'Singleton Pattern': `class DatabaseConnection {
    constructor() {
        if (DatabaseConnection.instance) {
            return DatabaseConnection.instance;
        }

        this.connection = this.createConnection();
        DatabaseConnection.instance = this;
        return this;
    }

    createConnection() {
        return { connected: true, host: 'localhost' };
    }

    query(sql) {
        console.log(\`Executing: \${sql}\`);
        return { results: [] };
    }
}

// Usage
const db1 = new DatabaseConnection();
const db2 = new DatabaseConnection();
console.log(db1 === db2); // true`,
                        'Observer Pattern': `class EventEmitter {
    constructor() {
        this.events = {};
    }

    subscribe(eventName, callback) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(callback);
    }

    unsubscribe(eventName, callback) {
        if (this.events[eventName]) {
            this.events[eventName] = this.events[eventName]
                .filter(cb => cb !== callback);
        }
    }

    emit(eventName, data) {
        if (this.events[eventName]) {
            this.events[eventName].forEach(callback => callback(data));
        }
    }
}

class NewsAgency extends EventEmitter {
    constructor() {
        super();
        this.news = '';
    }

    setNews(news) {
        this.news = news;
        this.emit('news', news);
    }
}

class NewsChannel {
    constructor(name) {
        this.name = name;
    }

    update(news) {
        console.log(\`\${this.name} broadcasting: \${news}\`);
    }
}

// Usage
const agency = new NewsAgency();
const cnn = new NewsChannel('CNN');
const bbc = new NewsChannel('BBC');

agency.subscribe('news', (news) => cnn.update(news));
agency.subscribe('news', (news) => bbc.update(news));

agency.setNews('Breaking: New JavaScript framework released!');`,
                        'Factory Pattern': `class VehicleFactory {
    static createVehicle(type, options) {
        switch (type.toLowerCase()) {
            case 'car':
                return new Car(options);
            case 'motorcycle':
                return new Motorcycle(options);
            case 'truck':
                return new Truck(options);
            default:
                throw new Error(\`Unknown vehicle type: \${type}\`);
        }
    }
}

class Vehicle {
    constructor(options) {
        this.make = options.make;
        this.model = options.model;
        this.year = options.year;
    }

    start() {
        console.log(\`\${this.make} \${this.model} is starting...\`);
    }
}

class Car extends Vehicle {
    constructor(options) {
        super(options);
        this.doors = options.doors || 4;
    }

    start() {
        console.log(\`Car \${this.make} \${this.model} is starting with \${this.doors} doors\`);
    }
}

class Motorcycle extends Vehicle {
    constructor(options) {
        super(options);
        this.engineSize = options.engineSize;
    }

    start() {
        console.log(\`Motorcycle \${this.make} \${this.model} (\${this.engineSize}cc) is starting\`);
    }
}

// Usage
const car = VehicleFactory.createVehicle('car', {
    make: 'Toyota',
    model: 'Camry',
    year: 2023,
    doors: 4
});

const bike = VehicleFactory.createVehicle('motorcycle', {
    make: 'Honda',
    model: 'CBR',
    year: 2023,
    engineSize: 600
});`
                    },
                    practice: [
                        'Implement Strategy pattern cho payment processing system',
                        'Tạo Command pattern cho undo/redo functionality',
                        'Xây dựng Decorator pattern cho coffee ordering system',
                        'Thiết kế State pattern cho vending machine'
                    ]
                },
                'arch-patterns': {
                    title: 'Architectural Patterns',
                    overview: 'Các mẫu kiến trúc phần mềm cấp cao giúp tổ chức và cấu trúc toàn bộ ứng dụng.',
                    keyPoints: [
                        'MVC (Model-View-Controller)',
                        'MVP (Model-View-Presenter)',
                        'MVVM (Model-View-ViewModel)',
                        'Clean Architecture',
                        'Hexagonal Architecture (Ports & Adapters)'
                    ],
                    details: {
                        'MVC Pattern': 'Tách biệt business logic (Model), presentation (View), và user input handling (Controller)',
                        'MVP Pattern': 'Presenter xử lý tất cả UI logic, View trở nên passive hơn',
                        'MVVM Pattern': 'ViewModel binding với View thông qua data binding, phổ biến trong WPF, Angular',
                        'Clean Architecture': 'Dependency rule: source code dependencies chỉ point inward',
                        'Event-Driven Architecture': 'Components communicate thông qua events, loose coupling'
                    },
                    examples: {
                        'MVC Pattern': `// Model
class UserModel {
    constructor() {
        this.users = [];
    }

    addUser(user) {
        this.users.push(user);
    }

    getUsers() {
        return this.users;
    }

    getUserById(id) {
        return this.users.find(user => user.id === id);
    }
}

// View
class UserView {
    displayUsers(users) {
        console.log('Users List:');
        users.forEach(user => {
            console.log(\`- \${user.name} (\${user.email})\`);
        });
    }

    displayUser(user) {
        console.log(\`User: \${user.name} - \${user.email}\`);
    }
}

// Controller
class UserController {
    constructor(model, view) {
        this.model = model;
        this.view = view;
    }

    addUser(name, email) {
        const user = {
            id: Date.now(),
            name,
            email
        };
        this.model.addUser(user);
    }

    showUsers() {
        const users = this.model.getUsers();
        this.view.displayUsers(users);
    }

    showUser(id) {
        const user = this.model.getUserById(id);
        if (user) {
            this.view.displayUser(user);
        }
    }
}`,
                        'Clean Architecture': `// Domain Layer (Entities)
class User {
    constructor(id, name, email) {
        this.id = id;
        this.name = name;
        this.email = email;
    }

    isValid() {
        return this.name && this.email && this.email.includes('@');
    }
}

// Use Cases (Application Layer)
class CreateUserUseCase {
    constructor(userRepository, emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }

    async execute(userData) {
        const user = new User(
            this.generateId(),
            userData.name,
            userData.email
        );

        if (!user.isValid()) {
            throw new Error('Invalid user data');
        }

        const existingUser = await this.userRepository.findByEmail(user.email);
        if (existingUser) {
            throw new Error('User already exists');
        }

        const savedUser = await this.userRepository.save(user);
        await this.emailService.sendWelcomeEmail(savedUser);

        return savedUser;
    }

    generateId() {
        return Date.now().toString();
    }
}

// Interface Adapters (Infrastructure Layer)
class UserRepository {
    constructor(database) {
        this.database = database;
    }

    async save(user) {
        return await this.database.insert('users', user);
    }

    async findByEmail(email) {
        return await this.database.findOne('users', { email });
    }
}

// Frameworks & Drivers (External Layer)
class DatabaseAdapter {
    async insert(table, data) {
        // Database implementation
        console.log(\`Inserting into \${table}:\`, data);
        return data;
    }

    async findOne(table, criteria) {
        // Database query implementation
        console.log(\`Finding in \${table}:\`, criteria);
        return null;
    }
}`
                    },
                    practice: [
                        'Implement MVC pattern cho một todo application',
                        'Thiết kế Clean Architecture cho e-commerce system',
                        'Tạo Event-Driven Architecture với message queues',
                        'Xây dựng Hexagonal Architecture với multiple adapters'
                    ]
                },
                'best-practices': {
                    title: 'Thực Hành Tốt Nhất',
                    overview: 'Các nguyên tắc và thực hành tốt nhất trong thiết kế và phát triển phần mềm.',
                    keyPoints: [
                        'Code Review và Pair Programming',
                        'Test-Driven Development (TDD)',
                        'Continuous Integration/Deployment',
                        'Documentation và Code Comments',
                        'Performance Optimization'
                    ],
                    details: {
                        'Code Review': 'Systematic examination của code để tìm bugs, improve quality, và knowledge sharing',
                        'TDD Cycle': 'Red (write failing test) → Green (make it pass) → Refactor (improve code)',
                        'CI/CD Pipeline': 'Automated testing, building, và deployment để ensure code quality',
                        'Documentation': 'README, API docs, architecture decisions, và inline comments',
                        'Performance': 'Profiling, monitoring, caching strategies, và optimization techniques'
                    },
                    examples: {
                        'TDD Example': `// 1. Red: Write failing test first
describe('Calculator', () => {
    test('should add two numbers correctly', () => {
        const calculator = new Calculator();
        const result = calculator.add(2, 3);
        expect(result).toBe(5);
    });

    test('should handle negative numbers', () => {
        const calculator = new Calculator();
        const result = calculator.add(-2, 3);
        expect(result).toBe(1);
    });
});

// 2. Green: Make the test pass
class Calculator {
    add(a, b) {
        return a + b;
    }
}

// 3. Refactor: Improve the code
class Calculator {
    add(a, b) {
        if (typeof a !== 'number' || typeof b !== 'number') {
            throw new Error('Both arguments must be numbers');
        }
        return a + b;
    }

    subtract(a, b) {
        if (typeof a !== 'number' || typeof b !== 'number') {
            throw new Error('Both arguments must be numbers');
        }
        return a - b;
    }
}`,
                        'Code Review Checklist': `// Code Review Checklist
const codeReviewChecklist = {
    functionality: [
        'Does the code do what it is supposed to do?',
        'Are edge cases handled properly?',
        'Is error handling appropriate?'
    ],

    design: [
        'Is the code well-designed and fit the overall architecture?',
        'Are design patterns used appropriately?',
        'Is the code following SOLID principles?'
    ],

    complexity: [
        'Is the code easy to understand?',
        'Are functions/methods too long or complex?',
        'Can any part be simplified?'
    ],

    tests: [
        'Are there appropriate unit tests?',
        'Do tests cover edge cases?',
        'Are tests readable and maintainable?'
    ],

    naming: [
        'Are variable and function names descriptive?',
        'Are naming conventions consistent?',
        'Are there any misleading names?'
    ],

    comments: [
        'Are comments clear and useful?',
        'Do comments explain why, not what?',
        'Is there any commented-out code to remove?'
    ],

    style: [
        'Does the code follow style guidelines?',
        'Is formatting consistent?',
        'Are there any style violations?'
    ]
};

// Performance Optimization Example
class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.memoizedResults = new Map();
    }

    // Memoization for expensive calculations
    fibonacci(n) {
        if (this.memoizedResults.has(n)) {
            return this.memoizedResults.get(n);
        }

        let result;
        if (n <= 1) {
            result = n;
        } else {
            result = this.fibonacci(n - 1) + this.fibonacci(n - 2);
        }

        this.memoizedResults.set(n, result);
        return result;
    }

    // Debouncing for frequent operations
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    // Lazy loading for resources
    async lazyLoad(resourceId, loader) {
        if (this.cache.has(resourceId)) {
            return this.cache.get(resourceId);
        }

        const resource = await loader();
        this.cache.set(resourceId, resource);
        return resource;
    }
}`
                    },
                    practice: [
                        'Setup CI/CD pipeline với GitHub Actions',
                        'Implement comprehensive test suite với Jest',
                        'Tạo code review guidelines cho team',
                        'Optimize performance của một slow application'
                    ]
                }
            },
            'data_structures': {
                'algorithms': {
                    title: 'Thuật Toán',
                    overview: 'Các thuật toán cơ bản và nâng cao để giải quyết các vấn đề computational phổ biến.',
                    keyPoints: [
                        'Sorting Algorithms (Bubble, Merge, Quick, Heap Sort)',
                        'Searching Algorithms (Linear, Binary, Hash-based)',
                        'Dynamic Programming (Memoization, Tabulation)',
                        'Greedy Algorithms (Activity Selection, Huffman)',
                        'Divide and Conquer (Merge Sort, Quick Sort)'
                    ],
                    details: {
                        'Sorting': 'Sắp xếp dữ liệu theo thứ tự. Stable vs Unstable, In-place vs Out-of-place',
                        'Searching': 'Tìm kiếm phần tử trong collection. Linear O(n), Binary O(log n)',
                        'Dynamic Programming': 'Optimal substructure + overlapping subproblems = DP',
                        'Greedy': 'Chọn local optimal choice ở mỗi step, hy vọng đạt global optimum',
                        'Divide & Conquer': 'Chia problem thành subproblems, solve recursively, combine results'
                    },
                    examples: {
                        'Merge Sort': `function mergeSort(arr) {
    if (arr.length <= 1) {
        return arr;
    }

    const mid = Math.floor(arr.length / 2);
    const left = mergeSort(arr.slice(0, mid));
    const right = mergeSort(arr.slice(mid));

    return merge(left, right);
}

function merge(left, right) {
    const result = [];
    let leftIndex = 0;
    let rightIndex = 0;

    while (leftIndex < left.length && rightIndex < right.length) {
        if (left[leftIndex] <= right[rightIndex]) {
            result.push(left[leftIndex]);
            leftIndex++;
        } else {
            result.push(right[rightIndex]);
            rightIndex++;
        }
    }

    // Add remaining elements
    while (leftIndex < left.length) {
        result.push(left[leftIndex]);
        leftIndex++;
    }

    while (rightIndex < right.length) {
        result.push(right[rightIndex]);
        rightIndex++;
    }

    return result;
}

// Time Complexity: O(n log n)
// Space Complexity: O(n)`,
                        'Dynamic Programming - Fibonacci': `// Naive recursive approach - O(2^n)
function fibonacciNaive(n) {
    if (n <= 1) return n;
    return fibonacciNaive(n - 1) + fibonacciNaive(n - 2);
}

// Memoization (Top-down DP) - O(n)
function fibonacciMemo(n, memo = {}) {
    if (n in memo) return memo[n];
    if (n <= 1) return n;

    memo[n] = fibonacciMemo(n - 1, memo) + fibonacciMemo(n - 2, memo);
    return memo[n];
}

// Tabulation (Bottom-up DP) - O(n)
function fibonacciTab(n) {
    if (n <= 1) return n;

    const dp = new Array(n + 1);
    dp[0] = 0;
    dp[1] = 1;

    for (let i = 2; i <= n; i++) {
        dp[i] = dp[i - 1] + dp[i - 2];
    }

    return dp[n];
}

// Space optimized - O(1)
function fibonacciOptimized(n) {
    if (n <= 1) return n;

    let prev2 = 0;
    let prev1 = 1;

    for (let i = 2; i <= n; i++) {
        const current = prev1 + prev2;
        prev2 = prev1;
        prev1 = current;
    }

    return prev1;
}`,
                        'Binary Search': `function binarySearch(arr, target) {
    let left = 0;
    let right = arr.length - 1;

    while (left <= right) {
        const mid = Math.floor((left + right) / 2);

        if (arr[mid] === target) {
            return mid;
        } else if (arr[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    return -1; // Not found
}

// Recursive version
function binarySearchRecursive(arr, target, left = 0, right = arr.length - 1) {
    if (left > right) {
        return -1;
    }

    const mid = Math.floor((left + right) / 2);

    if (arr[mid] === target) {
        return mid;
    } else if (arr[mid] < target) {
        return binarySearchRecursive(arr, target, mid + 1, right);
    } else {
        return binarySearchRecursive(arr, target, left, mid - 1);
    }
}

// Time Complexity: O(log n)
// Space Complexity: O(1) iterative, O(log n) recursive`
                    },
                    practice: [
                        'Implement QuickSort với different pivot strategies',
                        'Solve Longest Common Subsequence với DP',
                        'Tạo Dijkstra algorithm cho shortest path',
                        'Implement KMP algorithm cho string matching'
                    ]
                },
                'complexity': {
                    title: 'Phân Tích Độ Phức Tạp',
                    overview: 'Big O notation và phân tích time/space complexity để đánh giá hiệu suất thuật toán.',
                    keyPoints: [
                        'Big O Notation - Upper bound của algorithm',
                        'Time Complexity - Thời gian thực thi theo input size',
                        'Space Complexity - Memory usage theo input size',
                        'Best, Average, Worst case analysis',
                        'Amortized Analysis cho data structures'
                    ],
                    details: {
                        'Big O Classes': 'O(1) < O(log n) < O(n) < O(n log n) < O(n²) < O(2^n) < O(n!)',
                        'Time Complexity': 'Đo lường số operations cần thiết khi input size tăng',
                        'Space Complexity': 'Đo lường memory usage, bao gồm auxiliary space',
                        'Amortized Analysis': 'Average performance over sequence of operations',
                        'Master Theorem': 'Giải recurrence relations cho divide-and-conquer algorithms'
                    },
                    examples: {
                        'Complexity Examples': `// O(1) - Constant Time
function getFirstElement(arr) {
    return arr[0]; // Always takes same time
}

// O(log n) - Logarithmic Time
function binarySearch(arr, target) {
    // Divides search space in half each iteration
    let left = 0, right = arr.length - 1;
    while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        if (arr[mid] === target) return mid;
        if (arr[mid] < target) left = mid + 1;
        else right = mid - 1;
    }
    return -1;
}

// O(n) - Linear Time
function findMax(arr) {
    let max = arr[0];
    for (let i = 1; i < arr.length; i++) { // n iterations
        if (arr[i] > max) max = arr[i];
    }
    return max;
}

// O(n log n) - Linearithmic Time
function mergeSort(arr) {
    // log n levels, each level does O(n) work
    if (arr.length <= 1) return arr;
    const mid = Math.floor(arr.length / 2);
    const left = mergeSort(arr.slice(0, mid));
    const right = mergeSort(arr.slice(mid));
    return merge(left, right);
}

// O(n²) - Quadratic Time
function bubbleSort(arr) {
    for (let i = 0; i < arr.length; i++) {     // n iterations
        for (let j = 0; j < arr.length - i - 1; j++) { // n iterations
            if (arr[j] > arr[j + 1]) {
                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
            }
        }
    }
    return arr;
}

// O(2^n) - Exponential Time
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2); // Each call makes 2 more calls
}`,
                        'Space Complexity Analysis': `// O(1) Space - Constant Space
function sumArray(arr) {
    let sum = 0; // Only uses fixed amount of extra space
    for (let i = 0; i < arr.length; i++) {
        sum += arr[i];
    }
    return sum;
}

// O(n) Space - Linear Space
function reverseArray(arr) {
    const reversed = []; // Creates new array of size n
    for (let i = arr.length - 1; i >= 0; i--) {
        reversed.push(arr[i]);
    }
    return reversed;
}

// O(log n) Space - Logarithmic Space (due to recursion stack)
function binarySearchRecursive(arr, target, left = 0, right = arr.length - 1) {
    if (left > right) return -1;
    const mid = Math.floor((left + right) / 2);
    if (arr[mid] === target) return mid;
    if (arr[mid] < target) {
        return binarySearchRecursive(arr, target, mid + 1, right);
    } else {
        return binarySearchRecursive(arr, target, left, mid - 1);
    }
}

// O(n) Space - Due to recursion stack depth
function fibonacciRecursive(n) {
    if (n <= 1) return n;
    return fibonacciRecursive(n - 1) + fibonacciRecursive(n - 2);
}`,
                        'Complexity Comparison Table': `// Algorithm Complexity Comparison
const algorithmComplexity = {
    sorting: {
        bubbleSort: { time: 'O(n²)', space: 'O(1)', stable: true },
        selectionSort: { time: 'O(n²)', space: 'O(1)', stable: false },
        insertionSort: { time: 'O(n²)', space: 'O(1)', stable: true },
        mergeSort: { time: 'O(n log n)', space: 'O(n)', stable: true },
        quickSort: { time: 'O(n log n) avg, O(n²) worst', space: 'O(log n)', stable: false },
        heapSort: { time: 'O(n log n)', space: 'O(1)', stable: false }
    },

    searching: {
        linearSearch: { time: 'O(n)', space: 'O(1)' },
        binarySearch: { time: 'O(log n)', space: 'O(1)' },
        hashTableSearch: { time: 'O(1) avg, O(n) worst', space: 'O(n)' }
    },

    dataStructures: {
        array: {
            access: 'O(1)',
            search: 'O(n)',
            insertion: 'O(n)',
            deletion: 'O(n)'
        },
        linkedList: {
            access: 'O(n)',
            search: 'O(n)',
            insertion: 'O(1)',
            deletion: 'O(1)'
        },
        hashTable: {
            access: 'N/A',
            search: 'O(1) avg',
            insertion: 'O(1) avg',
            deletion: 'O(1) avg'
        },
        binarySearchTree: {
            access: 'O(log n) avg',
            search: 'O(log n) avg',
            insertion: 'O(log n) avg',
            deletion: 'O(log n) avg'
        }
    }
};`
                    },
                    practice: [
                        'Phân tích complexity của recursive algorithms',
                        'So sánh time/space tradeoffs của different approaches',
                        'Optimize algorithms để giảm complexity',
                        'Tính toán amortized complexity của dynamic arrays'
                    ]
                }
            },
            'database': {
                'relational': {
                    title: 'Cơ Sở Dữ Liệu Quan Hệ',
                    overview: 'Relational databases sử dụng tables, rows, columns để lưu trữ dữ liệu với ACID properties và SQL query language.',
                    keyPoints: [
                        'ACID Properties (Atomicity, Consistency, Isolation, Durability)',
                        'Normalization (1NF, 2NF, 3NF, BCNF)',
                        'SQL Queries (SELECT, JOIN, Subqueries, Window Functions)',
                        'Indexing Strategies (B-tree, Hash, Composite)',
                        'Transaction Management và Concurrency Control'
                    ],
                    details: {
                        'ACID Properties': 'Atomicity: all-or-nothing, Consistency: valid state, Isolation: concurrent safety, Durability: permanent storage',
                        'Normalization': 'Eliminate redundancy: 1NF (atomic values), 2NF (no partial dependencies), 3NF (no transitive dependencies)',
                        'SQL Joins': 'INNER (matching rows), LEFT/RIGHT (include nulls), FULL OUTER (all rows), CROSS (cartesian product)',
                        'Indexing': 'B-tree cho range queries, Hash cho equality, Composite cho multiple columns',
                        'Isolation Levels': 'READ UNCOMMITTED < READ COMMITTED < REPEATABLE READ < SERIALIZABLE'
                    },
                    examples: {
                        'Advanced SQL Queries': `-- Window Functions for Analytics
SELECT
    employee_id,
    name,
    salary,
    department_id,
    -- Ranking within department
    RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as dept_rank,
    -- Running total
    SUM(salary) OVER (ORDER BY hire_date) as running_total,
    -- Moving average (last 3 employees)
    AVG(salary) OVER (ORDER BY hire_date ROWS BETWEEN 2 PRECEDING AND CURRENT ROW) as moving_avg,
    -- Lag/Lead for comparisons
    LAG(salary, 1) OVER (ORDER BY hire_date) as prev_salary,
    LEAD(salary, 1) OVER (ORDER BY hire_date) as next_salary
FROM employees;

-- Common Table Expressions (CTEs)
WITH RECURSIVE employee_hierarchy AS (
    -- Anchor: Top-level managers
    SELECT employee_id, name, manager_id, 0 as level,
           CAST(name AS VARCHAR(1000)) as path
    FROM employees
    WHERE manager_id IS NULL

    UNION ALL

    -- Recursive: Direct reports
    SELECT e.employee_id, e.name, e.manager_id, eh.level + 1,
           eh.path + ' -> ' + e.name
    FROM employees e
    JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id
)
SELECT * FROM employee_hierarchy ORDER BY level, path;

-- Complex Joins and Subqueries
SELECT
    d.department_name,
    COUNT(e.employee_id) as employee_count,
    AVG(e.salary) as avg_salary,
    MAX(e.salary) as max_salary,
    (SELECT AVG(salary) FROM employees) as company_avg
FROM departments d
LEFT JOIN employees e ON d.department_id = e.department_id
WHERE d.department_id IN (
    SELECT department_id
    FROM employees
    GROUP BY department_id
    HAVING COUNT(*) > 5
)
GROUP BY d.department_id, d.department_name
HAVING AVG(e.salary) > (SELECT AVG(salary) FROM employees)
ORDER BY avg_salary DESC;`,
                        'Database Design Example': `-- E-commerce Database Schema
CREATE TABLE users (
    user_id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE categories (
    category_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_category_id INTEGER REFERENCES categories(category_id)
);

CREATE TABLE products (
    product_id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL CHECK (price > 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    category_id INTEGER REFERENCES categories(category_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE orders (
    order_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(user_id),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
    total_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE order_items (
    order_item_id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(order_id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(product_id),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED
);

-- Indexes for performance
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_order_items_product ON order_items(product_id);

-- Composite index for common queries
CREATE INDEX idx_products_category_price ON products(category_id, price);`,
                        'Stored Procedures and Triggers': `-- Stored Procedure for Order Processing
DELIMITER //
CREATE PROCEDURE ProcessOrder(
    IN p_user_id INT,
    IN p_product_id INT,
    IN p_quantity INT,
    OUT p_order_id INT,
    OUT p_status VARCHAR(50)
)
BEGIN
    DECLARE v_stock INT;
    DECLARE v_price DECIMAL(10,2);
    DECLARE v_total DECIMAL(10,2);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_status = 'ERROR: Transaction failed';
    END;

    START TRANSACTION;

    -- Check stock availability
    SELECT stock_quantity, price INTO v_stock, v_price
    FROM products
    WHERE product_id = p_product_id FOR UPDATE;

    IF v_stock < p_quantity THEN
        SET p_status = 'ERROR: Insufficient stock';
        ROLLBACK;
    ELSE
        -- Calculate total
        SET v_total = v_price * p_quantity;

        -- Create order
        INSERT INTO orders (user_id, total_amount, status)
        VALUES (p_user_id, v_total, 'processing');

        SET p_order_id = LAST_INSERT_ID();

        -- Add order item
        INSERT INTO order_items (order_id, product_id, quantity, unit_price)
        VALUES (p_order_id, p_product_id, p_quantity, v_price);

        -- Update stock
        UPDATE products
        SET stock_quantity = stock_quantity - p_quantity
        WHERE product_id = p_product_id;

        COMMIT;
        SET p_status = 'SUCCESS: Order processed';
    END IF;
END //
DELIMITER ;

-- Trigger for audit logging
CREATE TABLE audit_log (
    log_id SERIAL PRIMARY KEY,
    table_name VARCHAR(50),
    operation VARCHAR(10),
    old_values JSON,
    new_values JSON,
    user_id INTEGER,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

DELIMITER //
CREATE TRIGGER products_audit_trigger
    AFTER UPDATE ON products
    FOR EACH ROW
BEGIN
    INSERT INTO audit_log (table_name, operation, old_values, new_values)
    VALUES (
        'products',
        'UPDATE',
        JSON_OBJECT('price', OLD.price, 'stock_quantity', OLD.stock_quantity),
        JSON_OBJECT('price', NEW.price, 'stock_quantity', NEW.stock_quantity)
    );
END //
DELIMITER ;`
                    },
                    practice: [
                        'Thiết kế database schema cho social media platform',
                        'Optimize slow queries bằng indexing strategies',
                        'Implement database backup và recovery procedures',
                        'Tạo complex reports với window functions và CTEs'
                    ]
                },
                'nosql': {
                    title: 'NoSQL Databases',
                    overview: 'NoSQL databases cung cấp flexible schema và horizontal scaling cho big data và real-time applications.',
                    keyPoints: [
                        'Document Stores (MongoDB, CouchDB)',
                        'Key-Value Stores (Redis, DynamoDB)',
                        'Column-Family (Cassandra, HBase)',
                        'Graph Databases (Neo4j, Amazon Neptune)',
                        'CAP Theorem và Eventual Consistency'
                    ],
                    details: {
                        'Document Stores': 'JSON-like documents, flexible schema, good cho content management',
                        'Key-Value': 'Simple key-value pairs, very fast, good cho caching và sessions',
                        'Column-Family': 'Wide columns, good cho time-series và analytics data',
                        'Graph': 'Nodes và relationships, good cho social networks và recommendations',
                        'CAP Theorem': 'Consistency, Availability, Partition tolerance - chỉ có thể có 2/3'
                    },
                    examples: {
                        'MongoDB Operations': `// MongoDB CRUD Operations
const { MongoClient } = require('mongodb');

class UserService {
    constructor(db) {
        this.collection = db.collection('users');
    }

    // Create
    async createUser(userData) {
        const result = await this.collection.insertOne({
            ...userData,
            createdAt: new Date(),
            updatedAt: new Date()
        });
        return result.insertedId;
    }

    // Read with complex queries
    async findUsers(filters = {}) {
        const pipeline = [
            { $match: filters },
            {
                $lookup: {
                    from: 'orders',
                    localField: '_id',
                    foreignField: 'userId',
                    as: 'orders'
                }
            },
            {
                $addFields: {
                    orderCount: { $size: '$orders' },
                    totalSpent: { $sum: '$orders.total' }
                }
            },
            { $sort: { totalSpent: -1 } }
        ];

        return await this.collection.aggregate(pipeline).toArray();
    }

    // Update
    async updateUser(userId, updateData) {
        return await this.collection.updateOne(
            { _id: userId },
            {
                $set: {
                    ...updateData,
                    updatedAt: new Date()
                }
            }
        );
    }

    // Delete
    async deleteUser(userId) {
        return await this.collection.deleteOne({ _id: userId });
    }

    // Advanced queries
    async getUserAnalytics() {
        return await this.collection.aggregate([
            {
                $group: {
                    _id: '$country',
                    userCount: { $sum: 1 },
                    avgAge: { $avg: '$age' },
                    totalSpent: { $sum: '$totalSpent' }
                }
            },
            { $sort: { userCount: -1 } }
        ]).toArray();
    }
}`,
                        'Redis Caching Patterns': `const redis = require('redis');
const client = redis.createClient();

class CacheService {
    constructor() {
        this.defaultTTL = 3600; // 1 hour
    }

    // Simple caching
    async get(key) {
        try {
            const value = await client.get(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            console.error('Cache get error:', error);
            return null;
        }
    }

    async set(key, value, ttl = this.defaultTTL) {
        try {
            await client.setex(key, ttl, JSON.stringify(value));
        } catch (error) {
            console.error('Cache set error:', error);
        }
    }

    // Cache-aside pattern
    async getOrSet(key, fetchFunction, ttl = this.defaultTTL) {
        let value = await this.get(key);

        if (value === null) {
            value = await fetchFunction();
            if (value !== null) {
                await this.set(key, value, ttl);
            }
        }

        return value;
    }

    // Distributed locking
    async acquireLock(lockKey, ttl = 10) {
        const lockValue = Date.now().toString();
        const result = await client.set(lockKey, lockValue, 'PX', ttl * 1000, 'NX');
        return result === 'OK' ? lockValue : null;
    }

    async releaseLock(lockKey, lockValue) {
        const script = \`
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
        \`;
        return await client.eval(script, 1, lockKey, lockValue);
    }

    // Pub/Sub for real-time features
    async publish(channel, message) {
        await client.publish(channel, JSON.stringify(message));
    }

    subscribe(channel, callback) {
        const subscriber = redis.createClient();
        subscriber.subscribe(channel);
        subscriber.on('message', (channel, message) => {
            callback(JSON.parse(message));
        });
        return subscriber;
    }

    // Rate limiting with sliding window
    async isRateLimited(key, limit, windowMs) {
        const now = Date.now();
        const pipeline = client.multi();

        // Remove old entries
        pipeline.zremrangebyscore(key, 0, now - windowMs);

        // Count current entries
        pipeline.zcard(key);

        // Add current request
        pipeline.zadd(key, now, now);

        // Set expiry
        pipeline.expire(key, Math.ceil(windowMs / 1000));

        const results = await pipeline.exec();
        const currentCount = results[1][1];

        return currentCount >= limit;
    }
}`
                    },
                    practice: [
                        'Thiết kế MongoDB schema cho blog platform',
                        'Implement Redis caching strategy cho e-commerce',
                        'Tạo real-time chat với Redis Pub/Sub',
                        'Compare performance: SQL vs NoSQL cho specific use case'
                    ]
                }
            },
            'devops': {
                'cloud-fundamentals': {
                    title: 'Cơ Bản Cloud Computing',
                    overview: 'Cloud Computing cung cấp on-demand access đến computing resources qua Internet với pay-as-you-use model.',
                    keyPoints: [
                        'Service Models: IaaS, PaaS, SaaS',
                        'Deployment Models: Public, Private, Hybrid, Multi-cloud',
                        'Core Services: Compute, Storage, Networking, Databases',
                        'Scalability: Horizontal vs Vertical scaling',
                        'Cost Optimization và Resource Management'
                    ],
                    details: {
                        'IaaS': 'Infrastructure as a Service - VMs, storage, networks (AWS EC2, Azure VMs)',
                        'PaaS': 'Platform as a Service - runtime environment (Heroku, Google App Engine)',
                        'SaaS': 'Software as a Service - complete applications (Gmail, Salesforce)',
                        'Auto Scaling': 'Automatically adjust resources based on demand',
                        'Load Balancing': 'Distribute traffic across multiple instances'
                    },
                    examples: {
                        'AWS Infrastructure as Code': `# Terraform configuration for AWS infrastructure
provider "aws" {
  region = "us-west-2"
}

# VPC and networking
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "main-vpc"
  }
}

resource "aws_subnet" "public" {
  count             = 2
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.\${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  map_public_ip_on_launch = true

  tags = {
    Name = "public-subnet-\${count.index + 1}"
  }
}

resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name = "main-igw"
  }
}

# Security group
resource "aws_security_group" "web" {
  name_prefix = "web-sg"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Auto Scaling Group
resource "aws_launch_template" "web" {
  name_prefix   = "web-template"
  image_id      = data.aws_ami.amazon_linux.id
  instance_type = "t3.micro"

  vpc_security_group_ids = [aws_security_group.web.id]

  user_data = base64encode(<<-EOF
    #!/bin/bash
    yum update -y
    yum install -y httpd
    systemctl start httpd
    systemctl enable httpd
    echo "<h1>Hello from \$(hostname -f)</h1>" > /var/www/html/index.html
  EOF
  )

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "web-server"
    }
  }
}

resource "aws_autoscaling_group" "web" {
  name                = "web-asg"
  vpc_zone_identifier = aws_subnet.public[*].id
  target_group_arns   = [aws_lb_target_group.web.arn]
  health_check_type   = "ELB"

  min_size         = 2
  max_size         = 10
  desired_capacity = 2

  launch_template {
    id      = aws_launch_template.web.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "web-asg"
    propagate_at_launch = false
  }
}

# Load Balancer
resource "aws_lb" "web" {
  name               = "web-lb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.web.id]
  subnets            = aws_subnet.public[*].id
}

resource "aws_lb_target_group" "web" {
  name     = "web-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = aws_vpc.main.id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }
}

resource "aws_lb_listener" "web" {
  load_balancer_arn = aws_lb.web.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.web.arn
  }
}`,
                        'Docker Containerization': `# Dockerfile for Node.js application
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app

# Copy built application
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["npm", "start"]

# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************/myapp
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=myapp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:`
                    },
                    practice: [
                        'Deploy application lên AWS sử dụng Terraform',
                        'Tạo multi-stage Docker build cho optimization',
                        'Setup auto-scaling policies cho traffic spikes',
                        'Implement blue-green deployment strategy'
                    ]
                }
            },
            'apis': {
                'rest': {
                    title: 'REST APIs',
                    overview: 'Representational State Transfer là architectural style cho designing networked applications sử dụng HTTP methods.',
                    keyPoints: [
                        'HTTP Methods: GET, POST, PUT, DELETE, PATCH',
                        'Status Codes: 2xx Success, 4xx Client Error, 5xx Server Error',
                        'Resource-based URLs và Stateless communication',
                        'Content Negotiation và Versioning',
                        'Authentication: JWT, OAuth 2.0, API Keys'
                    ],
                    details: {
                        'REST Principles': 'Stateless, Cacheable, Uniform Interface, Layered System, Client-Server',
                        'HTTP Methods': 'GET (read), POST (create), PUT (update/replace), PATCH (partial update), DELETE (remove)',
                        'Status Codes': '200 OK, 201 Created, 400 Bad Request, 401 Unauthorized, 404 Not Found, 500 Internal Server Error',
                        'API Versioning': 'URL versioning (/v1/), Header versioning, Query parameter versioning',
                        'Rate Limiting': 'Prevent abuse với token bucket, sliding window algorithms'
                    },
                    examples: {
                        'Express.js REST API': `const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const rateLimit = require('express-rate-limit');

const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP'
});
app.use('/api/', limiter);

// Authentication middleware
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid token' });
        }
        req.user = user;
        next();
    });
};

// Error handling middleware
const errorHandler = (err, req, res, next) => {
    console.error(err.stack);

    if (err.name === 'ValidationError') {
        return res.status(400).json({
            error: 'Validation Error',
            details: err.message
        });
    }

    if (err.name === 'CastError') {
        return res.status(400).json({
            error: 'Invalid ID format'
        });
    }

    res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
};

// Routes
// GET /api/users - Get all users with pagination
app.get('/api/users', authenticateToken, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        const users = await User.find()
            .select('-password')
            .skip(skip)
            .limit(limit)
            .sort({ createdAt: -1 });

        const total = await User.countDocuments();

        res.json({
            users,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        next(error);
    }
});

// POST /api/users - Create new user
app.post('/api/users', async (req, res, next) => {
    try {
        const { name, email, password } = req.body;

        // Validation
        if (!name || !email || !password) {
            return res.status(400).json({
                error: 'Name, email, and password are required'
            });
        }

        // Check if user exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(409).json({
                error: 'User with this email already exists'
            });
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Create user
        const user = new User({
            name,
            email,
            password: hashedPassword
        });

        await user.save();

        // Remove password from response
        const userResponse = user.toObject();
        delete userResponse.password;

        res.status(201).json({
            message: 'User created successfully',
            user: userResponse
        });
    } catch (error) {
        next(error);
    }
});

// PUT /api/users/:id - Update user
app.put('/api/users/:id', authenticateToken, async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, email } = req.body;

        // Check if user can update this resource
        if (req.user.id !== id && req.user.role !== 'admin') {
            return res.status(403).json({
                error: 'Forbidden: Cannot update other users'
            });
        }

        const user = await User.findByIdAndUpdate(
            id,
            { name, email, updatedAt: new Date() },
            { new: true, runValidators: true }
        ).select('-password');

        if (!user) {
            return res.status(404).json({
                error: 'User not found'
            });
        }

        res.json({
            message: 'User updated successfully',
            user
        });
    } catch (error) {
        next(error);
    }
});

// DELETE /api/users/:id - Delete user
app.delete('/api/users/:id', authenticateToken, async (req, res, next) => {
    try {
        const { id } = req.params;

        // Only admin or user themselves can delete
        if (req.user.id !== id && req.user.role !== 'admin') {
            return res.status(403).json({
                error: 'Forbidden: Cannot delete other users'
            });
        }

        const user = await User.findByIdAndDelete(id);

        if (!user) {
            return res.status(404).json({
                error: 'User not found'
            });
        }

        res.status(204).send();
    } catch (error) {
        next(error);
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

app.use(errorHandler);

module.exports = app;`,
                        'API Documentation with OpenAPI': `# OpenAPI 3.0 Specification
openapi: 3.0.3
info:
  title: User Management API
  description: RESTful API for managing users
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.example.com/v1
    description: Production server
  - url: https://staging-api.example.com/v1
    description: Staging server

paths:
  /users:
    get:
      summary: Get all users
      description: Retrieve a paginated list of users
      tags:
        - Users
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create a new user
      description: Create a new user account
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        email:
          type: string
          format: email
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - name
        - email
        - createdAt
        - updatedAt

    CreateUserRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
      required:
        - name
        - email
        - password

    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        pages:
          type: integer

    Error:
      type: object
      properties:
        error:
          type: string
        details:
          type: string
      required:
        - error

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT`
                    },
                    practice: [
                        'Tạo RESTful API với proper error handling',
                        'Implement API versioning strategy',
                        'Setup API documentation với Swagger/OpenAPI',
                        'Add rate limiting và authentication middleware'
                    ]
                }
            },
            'ai_ml': {
                'math-foundations': {
                    title: 'Nền Tảng Toán Học cho AI/ML',
                    overview: 'Các kiến thức toán học cơ bản cần thiết để hiểu và implement machine learning algorithms.',
                    keyPoints: [
                        'Linear Algebra: Vectors, Matrices, Eigenvalues',
                        'Calculus: Derivatives, Gradients, Chain Rule',
                        'Statistics & Probability: Distributions, Bayes Theorem',
                        'Optimization: Gradient Descent, Convex Optimization',
                        'Information Theory: Entropy, KL Divergence'
                    ],
                    details: {
                        'Linear Algebra': 'Vector operations, matrix multiplication, eigendecomposition cho PCA và neural networks',
                        'Calculus': 'Partial derivatives cho backpropagation, gradients cho optimization',
                        'Statistics': 'Probability distributions, hypothesis testing, confidence intervals',
                        'Optimization': 'Finding minima/maxima của loss functions, gradient-based methods',
                        'Information Theory': 'Measuring information content, uncertainty, model comparison'
                    },
                    examples: {
                        'Linear Algebra in ML': `import numpy as np
import matplotlib.pyplot as plt

# Vector operations
def vector_operations():
    # Vectors as numpy arrays
    v1 = np.array([1, 2, 3])
    v2 = np.array([4, 5, 6])

    # Dot product (scalar)
    dot_product = np.dot(v1, v2)  # 1*4 + 2*5 + 3*6 = 32

    # Cross product (vector)
    cross_product = np.cross(v1, v2)

    # Vector norm (magnitude)
    norm = np.linalg.norm(v1)  # sqrt(1^2 + 2^2 + 3^2)

    # Unit vector
    unit_vector = v1 / norm

    return dot_product, cross_product, norm, unit_vector

# Matrix operations for neural networks
def matrix_operations():
    # Weight matrix (3x2) and input vector (2x1)
    W = np.array([[0.1, 0.2],
                  [0.3, 0.4],
                  [0.5, 0.6]])

    x = np.array([[1.0],
                  [2.0]])

    # Forward pass: y = Wx + b
    b = np.array([[0.1],
                  [0.2],
                  [0.3]])

    y = np.dot(W, x) + b

    # Activation function (sigmoid)
    def sigmoid(z):
        return 1 / (1 + np.exp(-z))

    activated = sigmoid(y)

    return y, activated

# Principal Component Analysis (PCA)
def pca_example():
    # Generate sample data
    np.random.seed(42)
    data = np.random.randn(100, 2)
    data[:, 1] = data[:, 0] * 0.5 + np.random.randn(100) * 0.1

    # Center the data
    data_centered = data - np.mean(data, axis=0)

    # Compute covariance matrix
    cov_matrix = np.cov(data_centered.T)

    # Eigendecomposition
    eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

    # Sort by eigenvalues (descending)
    idx = np.argsort(eigenvalues)[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]

    # Transform data to principal components
    pca_data = np.dot(data_centered, eigenvectors)

    return data, pca_data, eigenvalues, eigenvectors

# Gradient computation for optimization
def gradient_example():
    # Simple quadratic function: f(x) = x^2 + 2x + 1
    def f(x):
        return x**2 + 2*x + 1

    # Analytical gradient: f'(x) = 2x + 2
    def gradient(x):
        return 2*x + 2

    # Numerical gradient (finite differences)
    def numerical_gradient(x, h=1e-5):
        return (f(x + h) - f(x - h)) / (2 * h)

    # Gradient descent optimization
    def gradient_descent(start_x, learning_rate=0.1, iterations=100):
        x = start_x
        history = [x]

        for i in range(iterations):
            grad = gradient(x)
            x = x - learning_rate * grad
            history.append(x)

            if abs(grad) < 1e-6:  # Convergence check
                break

        return x, history

    # Find minimum starting from x=5
    optimal_x, history = gradient_descent(5.0)

    return optimal_x, history`,
                        'Statistics and Probability': `import numpy as np
import scipy.stats as stats
import matplotlib.pyplot as plt

# Probability distributions
def probability_distributions():
    # Normal distribution
    mu, sigma = 0, 1
    normal_dist = stats.norm(mu, sigma)

    # Generate samples
    samples = normal_dist.rvs(1000)

    # Probability density function
    x = np.linspace(-4, 4, 100)
    pdf = normal_dist.pdf(x)

    # Cumulative distribution function
    cdf = normal_dist.cdf(x)

    return samples, x, pdf, cdf

# Bayes' Theorem example
def bayes_theorem_example():
    """
    Medical test example:
    - Disease prevalence: 1%
    - Test sensitivity (true positive rate): 95%
    - Test specificity (true negative rate): 90%

    Question: If test is positive, what's probability of having disease?
    """

    # Prior probabilities
    P_disease = 0.01  # 1% prevalence
    P_no_disease = 1 - P_disease

    # Likelihoods
    P_positive_given_disease = 0.95  # Sensitivity
    P_positive_given_no_disease = 1 - 0.90  # 1 - Specificity

    # Marginal probability of positive test
    P_positive = (P_positive_given_disease * P_disease +
                  P_positive_given_no_disease * P_no_disease)

    # Posterior probability using Bayes' theorem
    P_disease_given_positive = (P_positive_given_disease * P_disease) / P_positive

    return P_disease_given_positive

# Maximum Likelihood Estimation
def maximum_likelihood_estimation():
    # Generate data from normal distribution
    true_mu, true_sigma = 5, 2
    data = np.random.normal(true_mu, true_sigma, 1000)

    # MLE for normal distribution
    # For normal distribution, MLE estimates are:
    mu_mle = np.mean(data)
    sigma_mle = np.std(data, ddof=0)  # Population standard deviation

    # Log-likelihood function
    def log_likelihood(mu, sigma, data):
        n = len(data)
        ll = -n/2 * np.log(2 * np.pi * sigma**2)
        ll -= np.sum((data - mu)**2) / (2 * sigma**2)
        return ll

    # Compute log-likelihood for MLE estimates
    ll_mle = log_likelihood(mu_mle, sigma_mle, data)

    return mu_mle, sigma_mle, ll_mle, true_mu, true_sigma

# Hypothesis testing
def hypothesis_testing():
    # Two-sample t-test
    # H0: means are equal, H1: means are different

    group1 = np.random.normal(10, 2, 50)  # Mean = 10
    group2 = np.random.normal(12, 2, 50)  # Mean = 12

    # Perform t-test
    t_statistic, p_value = stats.ttest_ind(group1, group2)

    # Effect size (Cohen's d)
    pooled_std = np.sqrt(((len(group1) - 1) * np.var(group1, ddof=1) +
                         (len(group2) - 1) * np.var(group2, ddof=1)) /
                        (len(group1) + len(group2) - 2))

    cohens_d = (np.mean(group1) - np.mean(group2)) / pooled_std

    # Confidence interval for difference in means
    diff_mean = np.mean(group1) - np.mean(group2)
    se_diff = pooled_std * np.sqrt(1/len(group1) + 1/len(group2))

    # 95% confidence interval
    alpha = 0.05
    df = len(group1) + len(group2) - 2
    t_critical = stats.t.ppf(1 - alpha/2, df)

    ci_lower = diff_mean - t_critical * se_diff
    ci_upper = diff_mean + t_critical * se_diff

    return t_statistic, p_value, cohens_d, (ci_lower, ci_upper)`
                    },
                    practice: [
                        'Implement PCA from scratch sử dụng eigendecomposition',
                        'Tạo gradient descent optimizer cho linear regression',
                        'Solve Bayes classification problem với real data',
                        'Implement statistical hypothesis tests'
                    ]
                }
            },
            'soft_skills': {
                'systems-thinking': {
                    title: 'Tư Duy Hệ Thống',
                    overview: 'Systems thinking là khả năng nhìn nhận và hiểu các mối quan hệ phức tạp giữa các thành phần trong một hệ thống.',
                    keyPoints: [
                        'Holistic View - Nhìn toàn cảnh thay vì từng phần riêng lẻ',
                        'Feedback Loops - Hiểu các vòng phản hồi trong hệ thống',
                        'Mental Models - Xây dựng mô hình tư duy hiệu quả',
                        'Root Cause Analysis - Tìm nguyên nhân gốc rễ',
                        'Unintended Consequences - Dự đoán tác động phụ'
                    ],
                    details: {
                        'Systems Perspective': 'Nhìn nhận problems như part của larger system, không isolated incidents',
                        'Feedback Loops': 'Reinforcing loops (exponential growth/decline) vs Balancing loops (stability)',
                        'Mental Models': 'Internal representations của external reality, shape how we interpret information',
                        'Leverage Points': 'Places trong system where small change có thể produce big impact',
                        'Emergence': 'System properties không thể predict từ individual components'
                    },
                    examples: {
                        'Systems Thinking Framework': `// Systems Thinking Analysis Framework
class SystemsAnalysis {
    constructor(problemStatement) {
        this.problem = problemStatement;
        this.stakeholders = [];
        this.components = [];
        this.relationships = [];
        this.feedbackLoops = [];
        this.leveragePoints = [];
    }

    // Step 1: Identify stakeholders
    identifyStakeholders() {
        const stakeholderTypes = [
            'Primary Users',
            'Secondary Users',
            'Decision Makers',
            'Implementers',
            'Affected Parties',
            'Influencers'
        ];

        return stakeholderTypes.map(type => ({
            type,
            interests: [],
            influence: 'low|medium|high',
            impact: 'low|medium|high'
        }));
    }

    // Step 2: Map system components
    mapComponents() {
        return {
            inputs: [], // What goes into the system
            processes: [], // What transforms inputs
            outputs: [], // What the system produces
            outcomes: [], // Desired results
            constraints: [], // Limitations and boundaries
            resources: [] // Available assets
        };
    }

    // Step 3: Identify relationships
    mapRelationships() {
        return [
            {
                from: 'component A',
                to: 'component B',
                type: 'influences|depends_on|enables|constrains',
                strength: 'weak|moderate|strong',
                direction: 'one_way|bidirectional'
            }
        ];
    }

    // Step 4: Find feedback loops
    identifyFeedbackLoops() {
        return [
            {
                type: 'reinforcing', // Amplifies change
                description: 'More X leads to more Y, which leads to more X',
                components: ['X', 'Y'],
                timeDelay: 'immediate|short|long'
            },
            {
                type: 'balancing', // Seeks equilibrium
                description: 'More X leads to less Y, which leads to more X',
                components: ['X', 'Y'],
                timeDelay: 'immediate|short|long'
            }
        ];
    }

    // Step 5: Find leverage points (Donella Meadows)
    findLeveragePoints() {
        return [
            {
                level: 1,
                type: 'Constants, numbers, subsidies',
                effectiveness: 'low',
                example: 'Changing budget allocations'
            },
            {
                level: 2,
                type: 'Material stocks and flows',
                effectiveness: 'low',
                example: 'Changing team size'
            },
            {
                level: 3,
                type: 'Regulating negative feedback loops',
                effectiveness: 'medium',
                example: 'Adding code review process'
            },
            {
                level: 4,
                type: 'Self-organization',
                effectiveness: 'high',
                example: 'Empowering teams to self-organize'
            },
            {
                level: 5,
                type: 'Goals',
                effectiveness: 'high',
                example: 'Changing success metrics'
            },
            {
                level: 6,
                type: 'Paradigms',
                effectiveness: 'very_high',
                example: 'Shifting from waterfall to agile mindset'
            }
        ];
    }

    // Root cause analysis using 5 Whys
    fiveWhys(problem) {
        const whys = [];
        let currentProblem = problem;

        for (let i = 0; i < 5; i++) {
            const why = \`Why does \${currentProblem} happen?\`;
            whys.push({
                question: why,
                answer: '', // To be filled by analyst
                evidence: [], // Supporting data
                assumptions: [] // What we're assuming
            });
            currentProblem = whys[i].answer;
        }

        return whys;
    }

    // Generate action plan
    generateActionPlan() {
        return {
            quickWins: [], // Low effort, high impact
            strategicInitiatives: [], // High effort, high impact
            experiments: [], // Test assumptions
            monitoring: [], // Track system health
            contingencies: [] // If things go wrong
        };
    }
}

// Example usage for software development problem
const problemAnalysis = new SystemsAnalysis(
    "Development team consistently misses deadlines"
);

// This framework helps identify that the problem might be:
// - Unrealistic estimation (process issue)
// - Unclear requirements (communication issue)
// - Technical debt (system constraint)
// - Scope creep (governance issue)
// Rather than just "developers are slow"`,
                        'Mental Models for Engineers': `// Collection of useful mental models for software engineers
const mentalModels = {
    // First Principles Thinking
    firstPrinciples: {
        description: 'Break down complex problems into fundamental truths',
        process: [
            'Identify assumptions',
            'Break down into basic elements',
            'Create new solutions from fundamentals'
        ],
        example: 'Instead of copying existing architecture, understand core requirements and build optimal solution'
    },

    // Inversion
    inversion: {
        description: 'Think backwards from desired outcome',
        process: [
            'Define what you want to achieve',
            'Identify what could prevent success',
            'Work backwards to current state'
        ],
        example: 'To prevent system outages, identify all failure modes and design defenses'
    },

    // Second-Order Thinking
    secondOrder: {
        description: 'Consider consequences of consequences',
        process: [
            'Identify immediate effects',
            'Consider what those effects cause',
            'Continue chain of causation'
        ],
        example: 'Adding caching improves performance, but increases complexity and potential consistency issues'
    },

    // Probabilistic Thinking
    probabilistic: {
        description: 'Think in terms of likelihoods, not certainties',
        process: [
            'Estimate probabilities of outcomes',
            'Consider confidence intervals',
            'Update beliefs with new evidence'
        ],
        example: 'This deployment has 95% chance of success, but 5% chance of rollback needed'
    },

    // Opportunity Cost
    opportunityCost: {
        description: 'Consider what you give up when making choices',
        process: [
            'Identify alternatives',
            'Estimate value of each option',
            'Choose highest value option'
        ],
        example: 'Spending time on performance optimization means less time for new features'
    },

    // Pareto Principle (80/20 Rule)
    pareto: {
        description: '80% of effects come from 20% of causes',
        applications: [
            '80% of bugs come from 20% of code',
            '80% of performance issues from 20% of bottlenecks',
            '80% of user value from 20% of features'
        ],
        example: 'Focus optimization efforts on the 20% of code that causes 80% of performance issues'
    },

    // Feedback Loops
    feedbackLoops: {
        description: 'Outputs of system fed back as inputs',
        types: {
            positive: 'Amplifies change (can lead to exponential growth or decline)',
            negative: 'Dampens change (creates stability)'
        },
        example: 'Technical debt creates positive feedback loop: more debt → slower development → more pressure → more shortcuts → more debt'
    },

    // Antifragility
    antifragility: {
        description: 'Systems that get stronger from stress',
        principles: [
            'Build redundancy',
            'Embrace controlled failure',
            'Learn from mistakes',
            'Decentralize decision making'
        ],
        example: 'Chaos engineering makes systems more resilient by intentionally introducing failures'
    }
};

// Framework for applying mental models
class MentalModelFramework {
    constructor() {
        this.models = mentalModels;
    }

    analyzeDecision(decision, context) {
        return {
            firstPrinciples: this.applyFirstPrinciples(decision),
            inversion: this.applyInversion(decision),
            secondOrder: this.applySecondOrder(decision),
            probabilistic: this.applyProbabilistic(decision),
            opportunityCost: this.applyOpportunityCost(decision, context)
        };
    }

    applyFirstPrinciples(decision) {
        return {
            assumptions: [], // What are we assuming?
            fundamentals: [], // What are the basic truths?
            alternatives: [] // What other approaches are possible?
        };
    }

    applyInversion(decision) {
        return {
            desiredOutcome: '',
            failureModes: [], // What could go wrong?
            preventiveMeasures: [] // How to prevent failures?
        };
    }

    applySecondOrder(decision) {
        return {
            immediateEffects: [],
            secondOrderEffects: [],
            thirdOrderEffects: [],
            timeHorizons: [] // When will effects manifest?
        };
    }

    applyProbabilistic(decision) {
        return {
            scenarios: [
                { outcome: 'best case', probability: 0.1 },
                { outcome: 'expected case', probability: 0.7 },
                { outcome: 'worst case', probability: 0.2 }
            ],
            confidenceLevel: 'low|medium|high',
            keyUncertainties: []
        };
    }

    applyOpportunityCost(decision, context) {
        return {
            alternatives: [],
            estimatedValue: {},
            tradeoffs: [],
            recommendation: ''
        };
    }
}`
                    },
                    practice: [
                        'Analyze một complex technical decision sử dụng systems thinking',
                        'Practice root cause analysis cho production incidents',
                        'Apply mental models để evaluate architecture choices',
                        'Map feedback loops trong development process'
                    ]
                }
            }
        };

        return contentDatabase[section]?.[topic] || {
            title: 'Content not found',
            overview: 'Nội dung đang được cập nhật...',
            keyPoints: [],
            details: {},
            examples: {},
            practice: []
        };
    }

    generateTopicHTML(contentData) {
        return `
            <div class="tab-content active" data-tab="overview">
                <h2>📖 Tổng quan</h2>
                <p>${contentData.overview}</p>
                
                <div class="highlight">
                    <h3>🎯 Điểm quan trọng:</h3>
                    <ul>
                        ${contentData.keyPoints.map(point => `<li>${point}</li>`).join('')}
                    </ul>
                </div>
            </div>
            
            <div class="tab-content" data-tab="details" style="display: none;">
                <h2>📚 Chi tiết</h2>
                ${Object.entries(contentData.details || {}).map(([key, value]) => `
                    <div class="info-box">
                        <h4>${key}</h4>
                        <p>${value}</p>
                    </div>
                `).join('')}
            </div>
            
            <div class="tab-content" data-tab="examples" style="display: none;">
                <h2>💻 Ví dụ Code</h2>
                ${Object.entries(contentData.examples || {}).map(([title, code]) => `
                    <div class="code-example">
                        <h4>${title}</h4>
                        <pre><code>${this.escapeHtml(code)}</code></pre>
                        <button class="code-copy-btn" onclick="app.copyToClipboard(\`${this.escapeForAttribute(code)}\`)">📋 Copy</button>
                    </div>
                `).join('')}
            </div>
            
            <div class="tab-content" data-tab="practice" style="display: none;">
                <h2>🎯 Thực hành</h2>
                ${(contentData.practice || []).map(item => `
                    <div class="warning-box">
                        <p><strong>Bài tập:</strong> ${item}</p>
                    </div>
                `).join('')}
                ${(contentData.practice || []).length === 0 ? '<p>Bài tập thực hành đang được cập nhật...</p>' : ''}
            </div>
        `;
    }

    setupContentInteractions(container) {
        // Setup copy buttons
        container.querySelectorAll('.code-copy-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });
    }

    switchView(btn) {
        const view = btn.getAttribute('data-view');
        this.currentView = view;
        
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        
        // Show corresponding content
        const container = document.getElementById('dynamic-content');
        container.querySelectorAll('.tab-content').forEach(content => {
            content.style.display = 'none';
        });
        
        const targetContent = container.querySelector(`[data-tab="${view}"]`);
        if (targetContent) {
            targetContent.style.display = 'block';
        }
    }

    toggleBookmark() {
        const key = `${this.currentSection}-${this.currentTopic}`;
        const btn = document.getElementById('bookmarkBtn');
        
        if (this.bookmarks.has(key)) {
            this.bookmarks.delete(key);
            btn.classList.remove('bookmarked');
            this.showNotification('Đã xóa bookmark! 📌');
        } else {
            this.bookmarks.add(key);
            btn.classList.add('bookmarked');
            this.showNotification('Đã thêm bookmark! ⭐');
        }
        
        this.saveBookmarks();
    }

    saveBookmarks() {
        localStorage.setItem('handbook-bookmarks', JSON.stringify([...this.bookmarks]));
    }

    loadBookmarks() {
        const saved = localStorage.getItem('handbook-bookmarks');
        if (saved) {
            this.bookmarks = new Set(JSON.parse(saved));
        }
    }

    markAsVisited(section, topic = '') {
        const key = topic ? `${section}-${topic}` : section;
        if (!this.progress[key]) {
            this.progress[key] = { visited: true, timestamp: Date.now() };
            this.saveProgress();
        }
    }

    updateProgress() {
        const totalItems = 36; // Approximate total topics
        const visitedItems = Object.keys(this.progress).length;
        const progressPercent = Math.round((visitedItems / totalItems) * 100);
        
        const progressText = document.querySelector('.progress-text');
        const progressFill = document.querySelector('.progress-fill');
        
        if (progressText) progressText.textContent = `Tiến độ: ${progressPercent}%`;
        if (progressFill) progressFill.style.width = `${progressPercent}%`;
    }

    saveProgress() {
        localStorage.setItem('handbook-progress', JSON.stringify(this.progress));
    }

    loadProgress() {
        const saved = localStorage.getItem('handbook-progress');
        if (saved) {
            this.progress = JSON.parse(saved);
        }
    }

    showHome() {
        this.currentSection = 'home';
        this.currentTopic = '';
        
        document.getElementById('section-content').classList.remove('active');
        document.getElementById('home').classList.add('active');
        
        // Update breadcrumb
        const breadcrumb = document.getElementById('breadcrumb');
        breadcrumb.innerHTML = '<span class="breadcrumb-item active">🏠 Trang chủ</span>';
        
        // Clear active navigation
        document.querySelectorAll('.nav-subitem.active').forEach(item => {
            item.classList.remove('active');
        });
        
        // Clear search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';
    }

    navigateToTopicFromCard(section, topic) {
        // Find and expand the corresponding nav section
        const navSection = document.querySelector(`[data-section="${section}"]`);
        if (navSection && !navSection.classList.contains('expanded')) {
            navSection.classList.add('expanded');
        }
        
        // Find and click the corresponding nav item
        const navItem = document.querySelector(`[data-topic="${topic}"]`);
        if (navItem) {
            navItem.click();
        }
    }

    openModal(title, code) {
        const modal = document.getElementById('codeModal');
        const modalTitle = document.getElementById('modal-title');
        const modalCode = document.getElementById('modal-code');
        
        modalTitle.textContent = title;
        modalCode.textContent = code;
        
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        const modal = document.getElementById('codeModal');
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    copyCode() {
        const code = document.getElementById('modal-code').textContent;
        this.copyToClipboard(code);
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('Đã copy vào clipboard! 📋');
        } catch (err) {
            console.error('Failed to copy:', err);
            // Fallback for older browsers
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            this.showNotification('Đã copy vào clipboard! 📋');
        }
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-success);
            color: var(--color-btn-primary-text);
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 2000;
            animation: slideIn 0.3s ease;
            box-shadow: var(--shadow-md);
        `;
        
        document.body.appendChild(notification);
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    setupMobileNav() {
        // Mobile navigation toggle
        if (window.innerWidth <= 768) {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.createElement('div');
            overlay.className = 'mobile-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 150;
                display: none;
            `;
            document.body.appendChild(overlay);
            
            // Add mobile menu button to header
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '☰';
            mobileMenuBtn.className = 'mobile-menu-btn';
            mobileMenuBtn.style.cssText = `
                display: block;
                background: var(--color-primary);
                color: var(--color-btn-primary-text);
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 18px;
                cursor: pointer;
            `;
            
            const headerControls = document.querySelector('.header-controls');
            headerControls.insertBefore(mobileMenuBtn, headerControls.firstChild);
            
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.add('open');
                overlay.style.display = 'block';
            });
            
            overlay.addEventListener('click', () => {
                sidebar.classList.remove('open');
                overlay.style.display = 'none';
            });
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            document.getElementById('searchInput').focus();
        }
        
        // Escape to close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }
        
        // Ctrl/Cmd + B to toggle bookmark
        if ((e.ctrlKey || e.metaKey) && e.key === 'b' && this.currentSection !== 'home') {
            e.preventDefault();
            this.toggleBookmark();
        }
    }

    getTopicTitle(section, topic) {
        const titles = {
            'system-design': 'System Design',
            'clean-architecture': 'Clean Architecture + DDD',
            'microservices': 'Microservices',
            'ai-native': 'AI-Native Design',
            'javascript': 'JavaScript/TypeScript',
            'python': 'Python',
            'go': 'Go',
            'cpp-rust': 'C++/Rust',
            'solid': 'SOLID Principles',
            'gof': 'Gang of Four Patterns',
            'linear': 'Linear Structures',
            'nonlinear': 'Non-Linear Structures',
            'algorithms': 'Algorithms',
            'complexity': 'Complexity Analysis'
        };
        return titles[topic] || topic;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    escapeForAttribute(text) {
        return text.replace(/`/g, '\\`').replace(/\$/g, '\\$');
    }
}

// Initialize the application
const app = new ToolkitHandbook();

// Export for global access
window.app = app;